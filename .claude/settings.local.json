{"permissions": {"allow": ["<PERSON><PERSON>(claude -h)", "Bash(grep:*)", "Bash(ls:*)", "Bash(wc:*)", "Bash(yarn add:*)", "Bash(yarn test:*)", "Bash(yarn test:unit)", "Bash(rg:*)", "Bash(node:*)", "Bash(npm run lint)", "Bash(npx eslint:*)", "Bash(yarn serve)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n -A 10 -B 5 \"createElementFromState\" /home/<USER>/code/simulaction/src/animation.js)"], "deny": []}}