import { createStore } from "vuex";
import {
  deepClone,
  diagramMode,
  getDeserializedWorkspaceWithMetadata,
} from "@/core";
import { v4 as uuidv4 } from "uuid";

const defaultState = {
  diagramName: "New diagram",
  diagramMode: "CREATION_MODE",
  selectionMode: "GRABBER",
  // Workspace management
  rootWorkspaceId: null,
  workspaceIds: [],
  currentWorkspaceId: null,

  // Position tracking for elements affected by workspace expansion/collapse
  elementPushHistory: new Map(), // Maps element ID to array of push operations

  // Global collections with workspace references
  sequences: new Map(),
  allSimpleElements: new Map(),
  allSimpleSegments: new Map(),

  millisecondsPerProcessingUnit: 2000,
  selectedElementsIds: [],
  selectedSegmentsIds: [],
  recordingVideo: false,
  showRightDrawer: false,
  showEditionBar: false,
  rifleMode: false,
  immersiveView: false,
  defaultShape: "SQUARE",
  headerTab: 0, // 0 -> Creation, 1-> Animation, 2-> Presentation
  rightDrawerHeaderTab: "elements",
  rightDrawerSequencesSubTab: "list",
  rightDrawerElementsSubTab: "edit",
  dirty: false,
  viewbox: {},
  tutorial: {
    started: false,
    currentStep: 0,
  },
  snapshots: [
    {
      allSimpleElements: new Map(),
      sequences: new Map(),
      currentSequenceId: null,
      selectedElementsIds: [],
      selectedSegmentsIds: [],
      millisecondsPerProcessingUnit: 2000,
      diagramMode: diagramMode.creationMode,
      time: new Date(),
      active: true,
      dirty: false,
    },
  ],
  loading: false,
  dragParams: {
    dragFromElementStarted: false,
    dragSourceElement: null,
    dragDestinationElement: null,
    isHoveringAnotherElement: false,
  },
  defaultFrameColor: "#000",
  autoIncrementFrame: true,
  autoReturnToFirstFrame: false,
  copyElementIds: [],
  snapToGrid: true,
  displayGrid: true,
  displayShadows: true,
  repeat: true,
  resetTour: false,
};
export const store = createStore({
  state: deepClone(defaultState),
  mutations: {
    updateDiagramName: (state, diagramName) => {
      state.diagramName = diagramName;
    },
    updateDiagramMode: (state, diagramMode) => {
      state.diagramMode = diagramMode;
    },
    updateSelectionMode: (state, selectionMode) => {
      state.selectionMode = selectionMode;
    },
    updateSequence: (state, payload) => {
      state.sequences = new Map(state.sequences); // This is for Vue to detect a change in state. Vue 3 solves this issue natively
      state.sequences.set(payload.id, deepClone(payload.sequence));
    },
    refreshSequences: (state, sequences) => {
      state.sequences = new Map(sequences);
    },
    updateCurrentSequenceId: (state, payload) => {
      state.currentSequenceId = payload;
    },
    updateElement: (state, payload) => {
      state.allSimpleElements = new Map(state.allSimpleElements);
      const foundElement = deepClone(state.allSimpleElements.get(payload.id));
      if (!foundElement) return;

      foundElement.name = payload.name;
      foundElement.workspaceParameters[state.currentWorkspaceId].x =
        payload.graphicElement.entity.cx();
      foundElement.workspaceParameters[state.currentWorkspaceId].y =
        payload.graphicElement.entity.cy();

      // Convert absolute text position to relative position
      const absoluteTextX = foundElement.type === "TEXT-CLASSIC"
        ? payload.graphicElement.elementName.x()
        : payload.graphicElement.elementName.ax();
      const absoluteTextY = foundElement.type === "TEXT-CLASSIC"
        ? payload.graphicElement.elementName.y()
        : payload.graphicElement.elementName.ay();

      foundElement.workspaceParameters[state.currentWorkspaceId].textX =
        absoluteTextX - foundElement.workspaceParameters[state.currentWorkspaceId].x;
      foundElement.workspaceParameters[state.currentWorkspaceId].textY =
        absoluteTextY - foundElement.workspaceParameters[state.currentWorkspaceId].y;
      foundElement.workspaceParameters[state.currentWorkspaceId].width =
        payload.graphicElement.entity.width();
      foundElement.workspaceParameters[state.currentWorkspaceId].height =
        payload.graphicElement.entity.height();
      // Ensure workspaceParameters exists for the current workspace
      if (!foundElement.workspaceParameters) {
        foundElement.workspaceParameters = {};
      }
      if (!foundElement.workspaceParameters[state.currentWorkspaceId]) {
        foundElement.workspaceParameters[state.currentWorkspaceId] = {};
      }

      // Anchors are now relative and don't need manual updates

      state.allSimpleElements.set(foundElement.id, foundElement);
    },
    updateSimpleElement: (state, simpleElement) => {
      state.allSimpleElements = new Map(state.allSimpleElements);
      state.allSimpleElements.set(
        simpleElement.id,
        structuredClone(simpleElement),
      );
    },
    createSegment: (state, segment) => {
      state.allSimpleSegments = new Map(state.allSimpleSegments);
      const simpleSegment = {
        id: segment.id,
        segmentElementsInfo: segment.segmentElementsInfo,
        parentWorkspaceId: segment.parentWorkspaceId,
      };
      state.allSimpleSegments.set(
        simpleSegment.id,
        structuredClone(simpleSegment),
      );
    },
    setCurrentFrameForCurrentSequence: (state, payload) => {
      state.sequences = new Map(state.sequences);
      state.sequences.get(payload.sequenceId).currentFrame =
        payload.currentFrame;
    },
    selectElement: (state, elementId) => {
      state.selectedElementsIds = [...state.selectedElementsIds];
      state.selectedElementsIds.push(elementId);
    },
    unselectElement: (state, elementId) => {
      state.selectedElementsIds = [...state.selectedElementsIds];
      state.selectedElementsIds.delete(elementId);
    },
    selectSegment: (state, segmentId) => {
      state.selectedSegmentsIds = [...state.selectedSegmentsIds];
      state.selectedSegmentsIds.push(segmentId);
    },
    clearSelectedElementsIds: (state) => {
      state.selectedElementsIds = [];
    },
    clearSelectedSegmentsIds: (state) => {
      state.selectedSegmentsIds = [];
    },
    deleteElement: (state, elementId) => {
      state.selectedElementsIds = [];
      state.allSimpleElements.delete(elementId);
      state.allSimpleElements = new Map(state.allSimpleElements);
    },
    deleteSegment: (state, segmentId) => {
      state.selectedSegmentsIds = [];
      state.allSimpleSegments.delete(segmentId);
      state.allSimpleSegments = new Map(state.allSimpleSegments);
    },
    updateSegment: (state, segment) => {
      state.allSimpleSegments = new Map(state.allSimpleSegments);
      state.allSimpleSegments.set(segment.id, segment);
    },
    updateSpeed: (state, millisecondsPerProcessingUnit) => {
      state.millisecondsPerProcessingUnit = millisecondsPerProcessingUnit;
    },
    resetState: (state) => {
      Object.assign(state, deepClone(defaultState));
    },
    updateSnapshotActive: (state, snapshot) => {
      state.snapshots[snapshot.index].active = snapshot.active;
    },
    updateSnapshots: (state, snapshots) => {
      state.snapshots = snapshots;
    },
    updateRecordingVideo: (state, value) => {
      state.recordingVideo = value;
    },
    showRightDrawer: (state, value) => {
      state.showRightDrawer = value;
    },
    showEditionBar: (state, value) => {
      state.showEditionBar = value;
    },
    toggleRifleMode: (state, value) => {
      state.rifleMode = value;
    },
    toggleImmersiveView: (state, value) => {
      state.immersiveView = value;
    },
    setViewbox: (state, viewbox) => {
      state.viewbox = viewbox;
    },
    setDefaultShape: (state, value) => {
      state.defaultShape = value;
    },
    setLatestSnapshotToNotDirty: (state) => {
      state.dirty = false;
      state.snapshots.forEach((snapshot) => {
        snapshot.dirty = true;
      });
      state.snapshots[state.snapshots.length - 1].dirty = false;
    },
    setAllSimpleElements: (state, allSimpleElements) => {
      state.allSimpleElements = allSimpleElements;
    },
    setAllSimpleSegments: (state, allSimpleSegments) => {
      state.allSimpleSegments = allSimpleSegments;
    },
    setSequences: (state, sequences) => {
      state.sequences = sequences;
    },
    setDiagramId: (state, diagramId) => {
      state.diagramId = diagramId;
    },
    updateDirtyActionAction: (state, value) => {
      state.dirty = value;
    },
    updateTutorial: (state, tutorial) => {
      state.tutorial = tutorial;
    },
    updateLoading: (state, value) => {
      state.loading = value;
    },
    updateDragParams: (state, value) => {
      state.dragParams.dragFromElementStarted = value.dragFromElementStarted;
      state.dragParams.dragSourceElement = value.dragSourceElement;
      state.dragParams.dragDestinationElement = value.dragDestinationElement;
    },
    updateHeaderTab: (state, value) => {
      state.headerTab = value;
    },
    updateRightDrawerHeaderTab: (state, value) => {
      state.rightDrawerHeaderTab = value;
    },
    updateRightDrawerSequencesSubTab: (state, value) => {
      state.rightDrawerSequencesSubTab = value;
    },
    updateRightDrawerElementsSubTab: (state, value) => {
      state.rightDrawerElementsSubTab = value;
    },
    updateCopyElementIds: (state, value) => {
      state.copyElementIds = value;
    },
    updateDisplayGrid: (state, value) => {
      state.displayGrid = value;
    },
    updateDisplayShadows: (state, value) => {
      state.displayShadows = value;
    },
    updateSnapToGrid: (state, value) => {
      state.snapToGrid = value;
    },
    updateAutoIncrementFrame: (state, value) => {
      state.autoIncrementFrame = value;
    },
    updateRepeat: (state, value) => {
      state.repeat = value;
    },
    updateResetTour: (state, value) => {
      state.resetTour = value;
    },
    initializeRootWorkspace: (state) => {
      const rootWorkspaceId = uuidv4();
      state.rootWorkspaceId = rootWorkspaceId;
      state.workspaceIds = [rootWorkspaceId];
      state.currentWorkspaceId = rootWorkspaceId;
    },
    setRootWorkspaceId: (state, rootWorkspaceId) => {
      state.rootWorkspaceId = rootWorkspaceId;
    },
    setWorkspaceIds: (state, workspaceIds) => {
      state.workspaceIds = workspaceIds;
    },
    createWorkspace: (state, { elementId, workspaceId }) => {
      // Check if element already has a workspace
      const element = state.allSimpleElements.get(elementId);
      if (element && element.childWorkspaceId) {
        return; // Element already has a workspace
      }

      state.workspaceIds.push(workspaceId);
      if (element) {
        element.childWorkspaceId = workspaceId;
      }
    },
    deleteWorkspace: (state, workspaceId) => {
      // Remove workspace from list
      state.workspaceIds = state.workspaceIds.filter(
        (id) => id !== workspaceId,
      );

      // If deleting current workspace, switch to root
      if (state.currentWorkspaceId === workspaceId) {
        state.currentWorkspaceId = state.rootWorkspaceId;
      }

      // Remove childWorkspaceId from parent element
      for (const element of state.allSimpleElements.values()) {
        if (element.childWorkspaceId === workspaceId) {
          element.childWorkspaceId = null;
          break;
        }
      }
    },
    enterWorkspace: (state, workspaceId) => {
      state.currentWorkspaceId = workspaceId;
    },

    addElementPush: (state, { elementId, workspaceElementId, pushX, pushY, workspaceId }) => {
      state.elementPushHistory = new Map(state.elementPushHistory);
      const history = state.elementPushHistory.get(elementId) || [];
      history.push({ workspaceElementId, pushX, pushY, workspaceId });
      state.elementPushHistory.set(elementId, history);
    },
    removeElementPushesForWorkspace: (state, { workspaceElementId }) => {
      state.elementPushHistory = new Map(state.elementPushHistory);
      for (const [elementId, pushHistory] of state.elementPushHistory.entries()) {
        const filteredHistory = pushHistory.filter(push => push.workspaceElementId !== workspaceElementId);
        if (filteredHistory.length === 0) {
          state.elementPushHistory.delete(elementId);
        } else {
          state.elementPushHistory.set(elementId, filteredHistory);
        }
      }
    },
  },
  getters: {
    currentWorkspaceElements: (state) => {
      return Array.from(state.allSimpleElements.values()).filter((element) => {
        return element.parentWorkspaceIds?.includes(state.currentWorkspaceId);
      });
    },
    currentWorkspaceSegments: (state) => {
      return Array.from(state.allSimpleSegments.values()).filter((segment) =>
        segment.parentWorkspaceId?.includes(state.currentWorkspaceId),
      );
    },
    currentWorkspaceSequences: (state) => {
      return Array.from(state.sequences.values()).filter(
        (sequence) => sequence.parentWorkspaceId === state.currentWorkspaceId,
      );
    },
    currentWorkspaceSelectedElementsIds: (state) => {
      return state.selectedElementsIds.filter((elementId) => {
        const element = state.allSimpleElements.get(elementId);
        if (!element) return false;
        return element.parentWorkspaceIds?.includes(state.currentWorkspaceId);
      });
    },

    workspaces: (state) => {
      return state.workspaceIds.map((id) => {
        const element = Array.from(state.allSimpleElements.values()).find(
          (element) => element.childWorkspaceId === id,
        );
        // Get the first parent workspace ID (for display purposes)
        const parentWorkspaceId = element?.parentWorkspaceIds[0];
        const parentElement = Array.from(state.allSimpleElements.values()).find(
          (childElement) => childElement.childWorkspaceId === parentWorkspaceId,
        );

        return {
          id,
          name: element ? element.name : "Root Workspace",
          elementId: element?.id,
          parentElementId: parentElement?.id,
          parentElementName: parentElement?.name
            ? parentElement?.name
            : parentWorkspaceId === state.rootWorkspaceId
              ? "root"
              : "none",
        };
      });
    },
  },
  actions: {
    updateSequenceAction: ({ commit }, payload) => {
      addToUndoAndCommit(commit, "updateSequence", payload, payload.undoable);
    },
    refreshSequencesAction: ({ commit }, { sequences, undoable }) => {
      addToUndoAndCommit(commit, "refreshSequences", sequences, undoable);
    },
    updateCurrentSequenceIdAction: ({ commit }, { sequenceId }) => {
      commit("updateCurrentSequenceId", sequenceId);
    },
    updateElement: ({ commit }, { element, undoable }) => {
      addToUndoAndCommit(commit, "updateElement", element, undoable);
    },
    updateSimpleElement: ({ commit }, { simpleElement, undoable }) => {
      addToUndoAndCommit(
        commit,
        "updateSimpleElement",
        simpleElement,
        undoable,
      );
    },
    createSegment: ({ commit }, { segment, undoable }) => {
      addToUndoAndCommit(commit, "createSegment", segment, undoable);
    },
    setCurrentFrameForCurrentSequenceAction: ({ commit }, payload) => {
      commit("setCurrentFrameForCurrentSequence", payload);
    },
    selectElementAction: ({ commit }, { elementId, undoable }) => {
      addToUndoAndCommit(commit, "selectElement", elementId, undoable);
    },
    unselectElementAction: ({ commit }, elementId) => {
      commit("unselectElement", elementId);
    },
    clearSelectedElementsIdsAction: ({ commit }) => {
      addToUndoAndCommit(commit, "clearSelectedElementsIds");
    },
    deleteElementAction: ({ commit }, { elementId, undoable }) => {
      addToUndoAndCommit(commit, "deleteElement", elementId, undoable);
    },
    selectSegmentAction: ({ commit }, { segmentId, undoable }) => {
      addToUndoAndCommit(commit, "selectSegment", segmentId, undoable);
    },
    clearSelectedSegmentsIdsAction: ({ commit }) => {
      addToUndoAndCommit(commit, "clearSelectedSegmentsIds");
    },
    deleteSegmentAction: ({ commit }, { segmentId, undoable }) => {
      addToUndoAndCommit(commit, "deleteSegment", segmentId, undoable);
    },
    updateSegmentAction: ({ commit }, { segment, undoable }) => {
      addToUndoAndCommit(commit, "updateSegment", segment, undoable);
    },
    updateSpeedAction: (
      { commit },
      { millisecondsPerProcessingUnit, undoable },
    ) => {
      addToUndoAndCommit(
        commit,
        "updateSpeed",
        millisecondsPerProcessingUnit,
        undoable,
      );
    },
    updateDiagramNameAction: ({ commit }, diagramName) => {
      commit("updateDiagramName", diagramName);
    },
    updateDiagramModeAction: ({ commit }, diagramMode) => {
      commit("updateDiagramMode", diagramMode);
    },
    updateSelectionModeAction: ({ commit }, selectionMode) => {
      commit("updateSelectionMode", selectionMode);
    },
    resetState: ({ commit }, undoable) => {
      addToUndoAndCommit(commit, "resetState", null, undoable);
    },
    updateSnapshotActive: ({ commit }, snapshot) => {
      commit("updateSnapshotActive", snapshot);
    },
    updateSnapshotsAction: ({ commit }, snapshots) => {
      commit("updateSnapshots", snapshots);
    },
    updateRecordingVideoAction: ({ commit }, value) => {
      commit("updateRecordingVideo", value);
    },
    showRightDrawerAction: ({ commit }, value) => {
      commit("showRightDrawer", value);
    },
    showEditionBarAction: ({ commit }, value) => {
      commit("showEditionBar", value);
    },
    toggleRifleModeAction: ({ commit }, value) => {
      commit("toggleRifleMode", value);
    },
    toggleImmersiveViewAction: ({ commit }, value) => {
      commit("toggleImmersiveView", value);
    },
    setViewboxAction: ({ commit }, viewbox) => {
      addToUndoAndCommit(commit, "setViewbox", viewbox, false);
    },
    setDefaultShapeAction: ({ commit }, value) => {
      commit("setDefaultShape", value);
    },
    setLatestSnapshotToNotDirtyAction: ({ commit }) => {
      addToUndoAndCommit(commit, "setLatestSnapshotToNotDirty", null, false);
    },
    setAllSimpleElementsAction: ({ commit }, allSimpleElements) => {
      commit("setAllSimpleElements", allSimpleElements);
    },
    setAllSimpleSegmentsAction: ({ commit }, allSimpleSegments) => {
      commit("setAllSimpleSegments", allSimpleSegments);
    },
    setSequencesAction: ({ commit }, sequences) => {
      commit("setSequences", sequences);
    },
    setDiagramIdAction: ({ commit }, diagramId) => {
      commit("setDiagramId", diagramId);
    },
    updateDirtyAction: ({ commit }, value) => {
      commit("updateDirtyActionAction", value);
    },
    updateTutorialAction: ({ commit }, tutorial) => {
      commit("updateTutorial", tutorial);
    },
    updateLoadingAction: ({ commit }, value) => {
      commit("updateLoading", value);
    },
    updateDragParamsAction: ({ commit }, value) => {
      commit("updateDragParams", value);
    },
    updateHeaderTabAction: ({ commit }, value) => {
      commit("updateHeaderTab", value);
    },
    updateRightDrawerHeaderTabAction: ({ commit }, value) => {
      commit("updateRightDrawerHeaderTab", value);
    },
    updateRightDrawerSequencesSubTabAction: ({ commit }, value) => {
      commit("updateRightDrawerSequencesSubTab", value);
    },
    updateRightDrawerElementsSubTabAction: ({ commit }, value) => {
      commit("updateRightDrawerElementsSubTab", value);
    },
    updateCopyElementIdsAction: ({ commit }, value) => {
      commit("updateCopyElementIds", value);
    },
    updateDisplayGridAction: ({ commit }, value) => {
      commit("updateDisplayGrid", value);
    },
    updateDisplayShadowsAction: ({ commit }, value) => {
      commit("updateDisplayShadows", value);
    },
    updateSnapToGridAction: ({ commit }, value) => {
      commit("updateSnapToGrid", value);
    },
    updateAutoIncrementFrameAction: ({ commit }, value) => {
      commit("updateAutoIncrementFrame", value);
    },
    updateRepeatAction: ({ commit }, value) => {
      commit("updateRepeat", value);
    },
    updateResetTourAction: ({ commit }, value) => {
      commit("updateResetTour", value);
    },
    initializeRootWorkspaceAction: ({ commit }) => {
      commit("initializeRootWorkspace");
    },
    setRootWorkspaceIdAction: ({ commit }, rootWorkspaceId) => {
      commit("setRootWorkspaceId", rootWorkspaceId);
    },
    setWorkspaceIdsAction: ({ commit }, workspaceIds) => {
      commit("setWorkspaceIds", workspaceIds);
    },
    createWorkspaceAction: ({ commit }, { elementId, workspaceId }) => {
      commit("createWorkspace", { elementId, workspaceId });
    },
    deleteWorkspaceAction: ({ commit }, { workspaceId, undoable }) => {
      addToUndoAndCommit(commit, "deleteWorkspace", workspaceId, undoable);
    },
    enterWorkspaceAction: ({ commit }, { workspaceId, undoable }) => {
      addToUndoAndCommit(commit, "enterWorkspace", workspaceId, undoable);
    },

    addElementPushAction: ({ commit }, { elementId, workspaceElementId, pushX, pushY, workspaceId }) => {
      commit("addElementPush", { elementId, workspaceElementId, pushX, pushY, workspaceId });
    },
    removeElementPushesForWorkspaceAction: ({ commit }, { workspaceElementId }) => {
      commit("removeElementPushesForWorkspace", { workspaceElementId });
    },
  },
  modules: {},
});

const addToUndoAndCommit = (commit, mutation, payload, undoable) => {
  commit(mutation, payload);
  if (undoable) {
    let snapshot = {
      allSimpleElements: deepClone(
        Array.from(store.state.allSimpleElements.values()),
      ),
      allSimpleSegments: deepClone(
        Array.from(store.state.allSimpleSegments.values()),
      ),
      sequences: deepClone(Array.from(store.state.sequences.values())),
      rootWorkspaceId: store.state.rootWorkspaceId,
      workspaceIds: deepClone(store.state.workspaceIds),
      currentWorkspaceId: store.state.currentWorkspaceId,
      currentSequenceId: store.state.currentSequenceId,
      selectedElementsIds: Array.from(store.state.selectedElementsIds),
      selectedSegmentsIds: Array.from(store.state.selectedSegmentsIds),
      millisecondsPerProcessingUnit: store.state.millisecondsPerProcessingUnit,
      time: new Date(),
      active: true,
      dirty: true,
      viewbox: store.state.viewbox,
      diagramMode: store.state.diagramMode,
    };
    let lastActiveIndex = store.state.snapshots.length - 1;
    for (let i = store.state.snapshots.length - 1; i >= 0; i--) {
      if (store.state.snapshots[i].active) {
        lastActiveIndex = i;
        break;
      } else {
        store.state.snapshots.splice(i, 1); // delete current element
      }
    }

    if (snapshot.time - store.state.snapshots[lastActiveIndex]?.time > 1000) {
      store.state.snapshots[lastActiveIndex].active = false;
      store.state.snapshots.push(snapshot);
      if (store.state.snapshots.length > 20) {
        store.state.snapshots.shift();
      }
    } else {
      store.state.snapshots[lastActiveIndex] = snapshot;
    }
  }
  if (!store.state.tutorial.started && mutation !== "resetState") {
    window.localStorage.setItem(
      "simulaction_autosave",
      JSON.stringify(getDeserializedWorkspaceWithMetadata()),
    );
  }
  if (mutation !== "setLatestSnapshotToNotDirty") {
    store.state.dirty = true;
  }
};

export default store;
