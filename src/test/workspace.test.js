import { store } from "@/store";
import {
  addWorkspaceToCurrentWorkspace,
  createNewElementAtPosition,
  createWorkspace,
  deepClone,
  deleteElement,
  deleteWorkspace,
  enterWorkspace,
} from "@/core";
import { v4 as uuidv4 } from "uuid";
import { draw } from "@/plugins/svgjs";
import { drawAdapter } from "@/draw-adapter";
import { createSequence } from "@/sequence";

const initialState = {
  diagramName: "New diagram",
  diagramMode: "CREATION_MODE",
  selectionMode: "GRABBER",
  sequences: new Map(),
  currentSequenceId: null,
  allSimpleElements: new Map(),
  allSimpleSegments: new Map(),
  millisecondsPerProcessingUnit: 2000,
  selectedElementsIds: [],
  selectedSegmentsIds: [],
  recordingVideo: false,
  showRightDrawer: false,
  showEditionBar: false,
  rifleMode: false,
  immersiveView: false,
  defaultShape: "SQUARE",
  dirty: false,
  viewbox: {},
  tutorial: {
    started: false,
    currentFrame: 0,
  },
  snapshots: [
    {
      allSimpleElements: new Map(),
      sequences: new Map(),
      currentSequenceId: null,
      selectedElementsIds: [],
      selectedSegmentsIds: [],
      millisecondsPerProcessingUnit: 2000,
      time: new Date(),
      active: true,
      dirty: false,
    },
  ],
  loading: false,
  dragParams: {
    dragFromElementStarted: false,
    dragSourceElement: null,
    dragDestinationElement: null,
  },
  defaultFrameColor: "#000",
  autoIncrementFrame: true,
  rootWorkspaceId: "rootWorkspaceId",
  currentWorkspaceId: "rootWorkspaceId",
  workspaceIds: ["rootWorkspaceId"],
  // Add element push history tracking
  elementPushHistory: new Map(),
};

jest.spyOn(draw, "viewbox").mockReturnValue({
  x: 150,
  y: 150,
  width: 1000,
  height: 1000,
});

Object.entries(drawAdapter).forEach(([key, value]) => {
  if (value instanceof Function) {
    jest.spyOn(drawAdapter, key).mockReturnValue({});
  }
});

beforeEach(() => {
  store.replaceState(deepClone(initialState));
});

const setElementInStore = async (element) => {
  await store.dispatch("updateSimpleElement", { simpleElement: element });
};

describe("Workspace management tests", () => {
  beforeEach(() => {
    store.replaceState(deepClone(initialState));
  });

  describe("Create workspace tests", () => {
    test("should create a new workspace for an element", async () => {
      const element = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "element1",
        100,
        100,
        "element1Id",
      );

      const workspaceId = await createWorkspace(element.id, "workspaceId");

      const foundElement = store.state.allSimpleElements.get(element.id);

      expect(workspaceId).toBeDefined();
      expect(store.state.workspaceIds).toContain(workspaceId);
      expect(foundElement.childWorkspaceId).toBe(workspaceId);
    });

    test("should not create a workspace for an element that already has one", async () => {
      const element = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "element1",
        100,
        100,
        "element1Id",
      );
      await setElementInStore(element);

      const workspaceId1 = await createWorkspace(element.id, "workspaceId1");
      const workspaceId2 = await createWorkspace(element.id, "workspaceId2");

      const foundElement = store.state.allSimpleElements.get(element.id);

      expect(workspaceId1).toBe(workspaceId1);
      expect(workspaceId2).toBe(workspaceId1);
      expect(store.state.workspaceIds).toHaveLength(2); // rootWorkspaceId and workspaceId1
      expect(foundElement.childWorkspaceId).toBe(workspaceId1);
    });
  });

  describe("Delete workspace tests", () => {
    test("should delete a workspace and clean up its elements", async () => {
      const element = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "element1",
        100,
        100,
        "element1Id",
      );
      await setElementInStore(element);

      const workspaceId = await createWorkspace(element.id, "workspaceId");
      await deleteWorkspace(workspaceId, true);

      expect(store.state.workspaceIds).not.toContain(workspaceId);
      expect(element.childWorkspaceId).toBeNull();
    });

    test("should switch to root workspace when deleting current workspace", async () => {
      const element = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "element1",
        100,
        100,
        "element1Id",
      );
      await setElementInStore(element);

      const workspaceId = await createWorkspace(element.id, "workspaceId");
      await enterWorkspace(workspaceId);
      await deleteWorkspace(workspaceId, true);

      expect(store.state.currentWorkspaceId).toBe(store.state.rootWorkspaceId);
    });
  });

  describe("Enter workspace tests", () => {
    test("should enter a workspace and update current workspace", async () => {
      const element = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "element1",
        100,
        100,
        "element1Id",
      );
      await setElementInStore(element);

      const workspaceId = await createWorkspace(element.id, "workspaceId");
      await enterWorkspace(workspaceId);

      expect(store.state.currentWorkspaceId).toBe(workspaceId);
    });

    test("should enter root workspace", async () => {
      const rootWorkspaceId = store.state.rootWorkspaceId;
      await enterWorkspace(rootWorkspaceId);

      expect(store.state.currentWorkspaceId).toBe(rootWorkspaceId);
    });
  });

  describe("Workspace Creation", () => {
    it("should create a workspace from an element", async () => {
      // Create an element
      const element = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element1Id",
      );

      // Create a workspace from the element
      const workspaceId = await createWorkspace(element.id, "workspaceId");

      // Verify workspace was created
      expect(store.state.workspaceIds).toContain(workspaceId);
      expect(store.state.currentWorkspaceId).toBe(store.state.rootWorkspaceId);

      // Verify element was updated with workspace ID
      const updatedElement = store.state.allSimpleElements.get(element.id);
      expect(updatedElement.childWorkspaceId).toBe(workspaceId);
    });

    it("should handle creating nested workspaces", async () => {
      // Create root element and workspace
      const rootElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Root Element",
        100,
        100,
        "rootElementId",
      );
      const rootWorkspaceId = await createWorkspace(
        rootElement.id,
        "rootWorkspaceId",
      );

      // Enter the root workspace
      await enterWorkspace(rootWorkspaceId);

      // Create child element and workspace
      const childElement = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child Element",
        100,
        100,
        "childElementId",
      );
      const childWorkspaceId = await createWorkspace(
        childElement.id,
        "childWorkspaceId",
      );

      // Get updated elements from store
      const foundChildElement = store.state.allSimpleElements.get(
        childElement.id,
      );

      // Verify workspace hierarchy
      expect(store.state.workspaceIds).toContain(rootWorkspaceId);
      expect(store.state.workspaceIds).toContain(childWorkspaceId);
      expect(foundChildElement.parentWorkspaceIds[0]).toBe(rootWorkspaceId);
      expect(foundChildElement.childWorkspaceId).toBe(childWorkspaceId);
    });
  });

  describe("Adding Workspace to Current Workspace", () => {
    test("should follow a complete workflow of creating workspaces and adding them to each other", async () => {
      const element1 = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "Element 1",
        100,
        100,
        "element1Id",
      );

      const workspace1Id = await createWorkspace(element1.id, "workspace1Id");

      const element2 = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Element 2",
        100,
        100,
        "element2Id",
      );

      const workspace2Id = await createWorkspace(element2.id, "workspace2Id");

      // Step 5: Enter the second workspace
      await enterWorkspace(workspace2Id);

      // Step 6: Add the first workspace to the second workspace
      const result = await addWorkspaceToCurrentWorkspace(workspace1Id);

      // Verify the result is true (success)
      expect(result).toBe(true);

      // Get the updated element1 from the store
      const updatedElement1 = store.state.allSimpleElements.get(element1.id);

      // Check that element1 now has workspace2 in its parentWorkspaceIds
      expect(updatedElement1.parentWorkspaceIds).toContain(workspace2Id);

      // Check that element1 has position data for workspace2
      expect(updatedElement1.workspaceParameters[workspace2Id]).toBeDefined();
      expect(updatedElement1.workspaceParameters[workspace2Id].x).toBeDefined();
      expect(updatedElement1.workspaceParameters[workspace2Id].y).toBeDefined();
      expect(
        updatedElement1.workspaceParameters[workspace2Id].anchors,
      ).toBeDefined();

      // Check that the original position data for root workspace is still intact
      expect(
        updatedElement1.workspaceParameters["rootWorkspaceId"],
      ).toBeDefined();
      expect(updatedElement1.workspaceParameters["rootWorkspaceId"].x).toBe(50);
      expect(updatedElement1.workspaceParameters["rootWorkspaceId"].y).toBe(50);
    });

    test("should handle circular workspace references gracefully", async () => {
      // Step 1: Create first element in root workspace
      const element1 = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "Element 1",
        100,
        100,
        "element1Id",
      );

      // Step 2: Create a workspace from the first element
      const workspace1Id = await createWorkspace(element1.id, "workspace1Id");

      // Step 3: Enter the first workspace
      await enterWorkspace(workspace1Id);

      // Step 4: Create second element in the first workspace
      const element2 = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Element 2",
        100,
        100,
        "element2Id",
      );

      // Step 5: Create a workspace from the second element
      const workspace2Id = await createWorkspace(element2.id, "workspace2Id");

      // Step 6: Enter the second workspace
      await enterWorkspace(workspace2Id);

      // Step 7: Try to add the root workspace to the second workspace
      // This creates a circular reference: root -> workspace1 -> workspace2 -> workspace1
      const result = await addWorkspaceToCurrentWorkspace(workspace1Id);

      // Verify the result is true (success) - the function should allow this
      expect(result).toBe(true);

      const foundElement1 = store.state.allSimpleElements.get(element1.id);

      // Check that the root element now has workspace2 in its parentWorkspaceIds
      expect(foundElement1.parentWorkspaceIds).toContain(workspace2Id);
    });

    test("should handle adding a workspace to itself gracefully", async () => {
      // Create an element representing workspace1
      const element1 = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "Workspace 1",
        100,
        100,
        "element1Id",
      );

      const workspace1Id = await createWorkspace(element1.id, "workspace1Id");

      // Try to add workspace1 to itself
      const result = await addWorkspaceToCurrentWorkspace(workspace1Id);

      // This should fail to prevent a direct self-reference
      expect(result).toBe(false);

      // The parentWorkspaceIds should remain unchanged
      const updatedElement = store.state.allSimpleElements.get(element1.id);
      expect(updatedElement.parentWorkspaceIds).not.toContain(workspace1Id);
    });

    test("should not add a workspace that is already in the current workspace", async () => {
      // Create a mock element for workspace2 that is already in workspace1
      const element1 = await createNewElementAtPosition(
        50,
        50,
        "SQUARE",
        "Element 1",
        100,
        100,
        "element1Id",
      );

      const workspace1Id = await createWorkspace(element1.id, "workspace1Id");

      await enterWorkspace(workspace1Id);

      const element2 = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Element 2",
        100,
        100,
        "element2Id",
      );

      const workspace2Id = await createWorkspace(element2.id, "workspace2Id");

      await enterWorkspace(workspace2Id);

      const result = await addWorkspaceToCurrentWorkspace(workspace1Id);

      // Check that the function returned false
      expect(result).toBe(true);

      const result2 = await addWorkspaceToCurrentWorkspace(workspace1Id);

      // Check that the function returned false
      expect(result2).toBe(false);

      const childrenOfWorkspace2 = Array.from(
        store.state.allSimpleElements.values(),
      ).filter((element) => element.parentWorkspaceIds.includes(workspace2Id));

      expect(childrenOfWorkspace2.length).toBe(1);
    });

    test("should return false if the workspace element is not found", async () => {
      // Call the function to add a non-existent workspace
      const result = await addWorkspaceToCurrentWorkspace("workspace2");

      // Check that the function returned false
      expect(result).toBe(false);
    });
  });

  describe("Workspace Deletion", () => {
    it("should delete a workspace and its elements", async () => {
      // Create element and workspace
      const element = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element1Id",
      );
      const workspaceId = await createWorkspace(element.id, "workspaceId");

      await enterWorkspace(workspaceId);

      const element2 = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element2Id",
      );

      const element3 = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element3Id",
      );

      // Delete the workspace
      await deleteWorkspace(workspaceId, true);

      // Get updated element from store
      const foundElement = store.state.allSimpleElements.get(element.id);
      const foundElement2 = store.state.allSimpleElements.get(element2.id);
      const foundElement3 = store.state.allSimpleElements.get(element3.id);

      // Verify workspace and elements belonging to it were deleted
      expect(store.state.workspaceIds).not.toContain(workspaceId);
      expect(foundElement.id).toBe(element.id);
      expect(foundElement2).toBeUndefined();
      expect(foundElement3).toBeUndefined();
    });

    it("should handle deleting a workspace with nested workspaces", async () => {
      // Create root element and workspace
      const rootElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Root Element",
        100,
        100,
        "rootElementId",
      );
      const parentWorkspaceId = await createWorkspace(
        rootElement.id,
        "parentWorkspaceId",
      );

      // Enter root workspace
      await enterWorkspace(parentWorkspaceId);

      // Create child element and workspace
      const childElement = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child Element",
        100,
        100,
        "childElementId",
      );
      const childWorkspaceId = await createWorkspace(
        childElement.id,
        "childWorkspaceId",
      );

      // Delete root workspace
      await deleteWorkspace(parentWorkspaceId, true);

      // Get updated elements from store
      const foundRootElement = store.state.allSimpleElements.get(
        rootElement.id,
      );
      const foundChildElement = store.state.allSimpleElements.get(
        childElement.id,
      );

      // Verify all workspaces and elements were deleted
      let workspaceIds = store.state.workspaceIds;
      expect(workspaceIds).not.toContain(childWorkspaceId);
      expect(workspaceIds).not.toContain(parentWorkspaceId);
      expect(foundRootElement.id).toBe(rootElement.id);
      expect(foundChildElement).toBeUndefined();
    });

    it("should handle deleting a workspace with multiple levels of nesting", async () => {
      // Create root element and workspace
      const rootElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Root Element",
        100,
        100,
        "rootElementId",
      );
      const rootWorkspaceId = await createWorkspace(
        rootElement.id,
        "rootWorkspaceId",
      );

      // Enter root workspace
      await enterWorkspace(rootWorkspaceId);

      // Create first level child
      const child1Element = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child 1",
        100,
        100,
        "child1ElementId",
      );
      const child1WorkspaceId = await createWorkspace(
        child1Element.id,
        "child1WorkspaceId",
      );

      // Enter first level child
      await enterWorkspace(child1WorkspaceId);

      // Create second level child
      const child2Element = await createNewElementAtPosition(
        300,
        300,
        "SQUARE",
        "Child 2",
        100,
        100,
        "child2ElementId",
      );
      const child2WorkspaceId = await createWorkspace(
        child2Element.id,
        "child2WorkspaceId",
      );

      // Delete middle workspace
      await deleteWorkspace(child1WorkspaceId, true);

      // Get updated elements from store
      const foundChild1Element = store.state.allSimpleElements.get(
        child1Element.id,
      );
      const foundChild2Element = store.state.allSimpleElements.get(
        child2Element.id,
      );

      let currentWorkspaceId = store.state.currentWorkspaceId;

      // Verify all nested workspaces and elements were deleted
      expect(store.state.workspaceIds).not.toContain(child1WorkspaceId);
      expect(store.state.workspaceIds).not.toContain(child2WorkspaceId);
      expect(foundChild1Element.id).toBe(child1Element.id);
      expect(foundChild2Element).toBeUndefined();
      expect(currentWorkspaceId).toBe(rootWorkspaceId);
    });

    it("should handle deleting a workspace with multiple child workspaces", async () => {
      // Create parent element and workspace
      const parentElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Parent Element",
        100,
        100,
        "parentElementId",
      );
      const parentWorkspaceId = await createWorkspace(
        parentElement.id,
        "parentWorkspaceId",
      );

      // Enter parent workspace
      await enterWorkspace(parentWorkspaceId);

      // Create multiple child elements and workspaces
      const child1Element = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child 1",
        100,
        100,
        "child1ElementId",
      );
      const child1WorkspaceId = await createWorkspace(
        child1Element.id,
        "child1WorkspaceId",
      );

      const child2Element = await createNewElementAtPosition(
        300,
        300,
        "SQUARE",
        "Child 2",
        100,
        100,
        "child2ElementId",
      );
      const child2WorkspaceId = await createWorkspace(
        child2Element.id,
        "child2WorkspaceId",
      );

      // Delete parent workspace
      await deleteWorkspace(parentWorkspaceId, true);

      // Get updated elements from store
      const foundParentElement = store.state.allSimpleElements.get(
        parentElement.id,
      );
      const foundChild1Element = store.state.allSimpleElements.get(
        child1Element.id,
      );
      const foundChild2Element = store.state.allSimpleElements.get(
        child2Element.id,
      );

      // Verify all child workspaces and elements were deleted
      let foundWorkspaceIds = store.state.workspaceIds;
      expect(foundWorkspaceIds).not.toContain(parentWorkspaceId);
      expect(foundWorkspaceIds).not.toContain(child1WorkspaceId);
      expect(foundWorkspaceIds).not.toContain(child2WorkspaceId);
      expect(foundParentElement.id).toBe(parentElement.id);
      expect(foundChild1Element).toBeUndefined();
      expect(foundChild2Element).toBeUndefined();
    });
    it("should not completely delete element if it is present in multiple workspaces but just remove reference", async () => {
      const element = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element1Id",
      );
      const workspaceId = await createWorkspace(element.id, "workspaceId");
      const element2 = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element2Id",
      );
      const workspaceId2 = await createWorkspace(element2.id, "workspaceId2");

      const element3 = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element3Id",
      );
      const workspaceId3 = await createWorkspace(element3.id, "workspaceId3");
      await enterWorkspace(workspaceId);
      await addWorkspaceToCurrentWorkspace(workspaceId3);
      await enterWorkspace(workspaceId2);
      await addWorkspaceToCurrentWorkspace(workspaceId3);
      // Now workspaceId3 is present in both workspaceId and workspaceId2

      await deleteElement({ elementId: element3.id, force: true });
      expect(store.state.allSimpleElements.has(element3.id)).toBe(true);
    });
    it("should handle deleting an element that has a child workspace", async () => {
      const parentElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Parent Element",
        100,
        100,
        "parentElementId",
      );
      const parentWorkspaceId = await createWorkspace(
        parentElement.id,
        "parentWorkspaceId",
      );

      // Enter parent workspace
      await enterWorkspace(parentWorkspaceId);

      const childElement = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child Element",
        100,
        100,
        "childElementId",
      );
      const childWorkspaceId = await createWorkspace(
        childElement.id,
        "childWorkspaceId",
      );

      await deleteElement({ elementId: parentElement.id, force: true });

      let foundWorkspaceIds = store.state.workspaceIds;
      let foundParentElement = store.state.allSimpleElements.get(
        parentElement.id,
      );
      let foundChildElement = store.state.allSimpleElements.get(
        childElement.id,
      );

      // Verify all child workspaces and elements were deleted
      expect(foundParentElement).toBeUndefined();
      expect(foundChildElement).toBeUndefined();
      expect(foundWorkspaceIds).not.toContain(parentWorkspaceId);
      expect(foundWorkspaceIds).not.toContain(childWorkspaceId);
    });
  });

  describe("Workspace Navigation", () => {
    it("should navigate between workspaces", async () => {
      // Create root element and workspace
      const rootElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Root Element",
        100,
        100,
        "rootElementId",
      );
      const rootWorkspaceId = await createWorkspace(
        rootElement.id,
        "rootWorkspaceId",
      );

      // Enter root workspace
      await enterWorkspace(rootWorkspaceId);

      // Create child element and workspace
      const childElement = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child Element",
        100,
        100,
        "childElementId",
      );
      const childWorkspaceId = await createWorkspace(
        childElement.id,
        "childWorkspaceId",
      );

      // Navigate to child workspace
      await enterWorkspace(childWorkspaceId);
      expect(store.state.currentWorkspaceId).toBe(childWorkspaceId);

      // Navigate back to root workspace
      await enterWorkspace(rootWorkspaceId);
      expect(store.state.currentWorkspaceId).toBe(rootWorkspaceId);
    });

    it("should handle navigation to non-existent workspace", async () => {
      const nonExistentId = uuidv4();
      await enterWorkspace(nonExistentId);
      expect(store.state.currentWorkspaceId).toBe(store.state.rootWorkspaceId);
    });
  });

  describe("Edge Cases", () => {
    it("should handle deleting a workspace while in it", async () => {
      // Create element and workspace
      const element = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element1Id",
      );
      const workspaceId = await createWorkspace(element.id, "workspaceId");

      // Enter workspace
      await enterWorkspace(workspaceId);

      // Delete workspace while in it
      await deleteWorkspace(workspaceId, true);

      // Verify we're back in root workspace
      expect(store.state.currentWorkspaceId).toBe(store.state.rootWorkspaceId);
    });
  });

  describe("Sequence Management in Workspaces", () => {
    it("should not directly create a sequence when creating a workspace", async () => {
      // Create element and workspace
      const element = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element1Id",
      );
      const workspaceId = await createWorkspace(element.id, "workspaceId");
      await enterWorkspace(workspaceId);

      // Get updated element from store
      const foundElement = store.state.allSimpleElements.get(element.id);

      // Verify sequence was created and linked to workspace
      expect(store.state.sequences.size).toBe(0);
      expect(foundElement).not.toBeUndefined();
    });

    it("should delete sequence when deleting workspace", async () => {
      // Create element and workspace
      const element = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Test Element",
        100,
        100,
        "element1Id",
      );
      const workspaceId = await createWorkspace(element.id, "workspaceId");

      // Get sequence ID before deletion
      const foundElement = store.state.allSimpleElements.get(element.id);
      const sequenceId = foundElement.sequenceId;

      // Delete workspace
      await deleteWorkspace(workspaceId, true);

      // Verify sequence was deleted
      expect(store.state.sequences.has(sequenceId)).toBe(false);
    });

    it("should delete all child sequences when deleting parent workspace", async () => {
      // Create root element and workspace
      const rootElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Root Element",
        100,
        100,
        "rootElementId",
      );
      const rootWorkspaceId = await createWorkspace(
        rootElement.id,
        "rootWorkspaceId",
      );

      // Enter root workspace
      await enterWorkspace(rootWorkspaceId);

      // Create child element and workspace
      const childElement = await createNewElementAtPosition(
        200,
        200,
        "SQUARE",
        "Child Element",
        100,
        100,
        "childElementId",
      );

      // Get sequence IDs before deletion
      const foundRootElement = store.state.allSimpleElements.get(
        rootElement.id,
      );
      const foundChildElement = store.state.allSimpleElements.get(
        childElement.id,
      );
      const rootSequenceId = foundRootElement.sequenceId;
      const childSequenceId = foundChildElement.sequenceId;

      // Delete root workspace
      await deleteWorkspace(rootWorkspaceId, true);

      // Verify all sequences were deleted
      expect(store.state.sequences.has(rootSequenceId)).toBe(false);
      expect(store.state.sequences.has(childSequenceId)).toBe(false);
    });

    it("should handle sequence management when deleting element with child workspace", async () => {
      // Create parent element and workspace
      const parentElement = await createNewElementAtPosition(
        100,
        100,
        "SQUARE",
        "Parent Element",
        100,
        100,
        "parentElementId",
      );
      const parentWorkspaceId = await createWorkspace(
        parentElement.id,
        "parentWorkspaceId",
      );

      // Enter parent workspace
      await enterWorkspace(parentWorkspaceId);

      await createSequence({ id: "seq1", name: "sequence1" });
      await createSequence({ id: "seq2", name: "sequence2" });

      const foundSequences = store.state.sequences;

      expect(foundSequences.size).toBe(2); // 2 because we don't automatically create a sequence upon entering the workspace

      await deleteElement({ elementId: parentElement.id, force: true });

      expect(foundSequences.size).toBe(0);
    });
  });
});
