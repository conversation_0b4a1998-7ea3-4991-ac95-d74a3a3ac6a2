import { draw } from "@/plugins/svgjs";
import { store } from "./store";
import {
  deepClone,
  diagramMode,
  getPositionFromCurrentView,
  getPositionFromStoredElement,
  isEmptyObject,
} from "@/core";

const { v4: uuidv4 } = require("uuid");

export const SEND_DATA = "sendData";
export const WAIT = "wait";
export const VIEWBOX = "viewboxPosition";
export const ELEMENT_POSITION = "elementPosition";
export const ELEMENT_OPACTITY = "elementOpacity";
export const IMPORTED_SEQUENCE = "importedSequence";
export const WORKSPACE_ZOOM_IN_1 = "workspaceZoomIn1";
export const WORKSPACE_ZOOM_IN_2 = "workspaceZoomIn2";
export const WORKSPACE_ZOOM_OUT_1 = "workspaceZoomOut1";
export const WORKSPACE_ZOOM_OUT_2 = "workspaceZoomOut2";

/**
 * Creates a new sequence with the provided parameters or default values
 * @param {Object} params - The parameters for the sequence
 * @returns {Object} The created sequence
 */
export const createSequence = async ({
  id,
  name,
  frames,
  isPlaying,
  currentFrame,
  parentWorkspaceId,
  undoable,
}) => {
  if (store.state.sequences.has(id)) {
    throw new Error(`Sequence with id ${id} already exists`);
  }
  const sequence = initialiseSequence(
    id,
    name,
    frames,
    isPlaying,
    currentFrame,
    parentWorkspaceId,
  );

  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence,
    undoable,
  });

  // we update currentSequenceId only if parentWorkspaceId is equal to currentwWorkspaceId
  if (sequence.parentWorkspaceId === store.state.currentWorkspaceId) {
    await store.dispatch("updateCurrentSequenceIdAction", {
      sequenceId: sequence.id,
    });
  }

  return sequence;
};

/**
 * Adds a send data action to the sequence
 * @param {string} elementSourceId - The source element ID
 * @param {string} elementDestinationId - The destination element ID
 * @param  sequence - The sequence
 */
export const addSendDataActionToSequence = async (
  elementSourceId,
  elementDestinationId,
  sequence,
) => {
  const elementSource = store.state.allSimpleElements.get(elementSourceId);
  const elementDestination =
    store.state.allSimpleElements.get(elementDestinationId);
  if (!elementSource || !elementDestination) {
    throw new Error("Source or destination element doesn't exist exists");
  }
  if (!sequence) {
    sequence = await createSequence({});
  }
  const currentSequenceId =
    sequence?.id || store.state.currentSequenceId || uuidv4();

  if (!store.state.currentSequenceId) {
    await store.dispatch("updateCurrentSequenceIdAction", {
      sequenceId: currentSequenceId,
    });
  }

  const nextFrame = sequence.currentFrame + 1;

  // Check if action is already set
  if (
    sequence.frames[nextFrame]?.some(
      (action) =>
        action.metadata.type === SEND_DATA &&
        action.data.elementSource === elementSourceId &&
        action.data.elementDestination === elementDestinationId,
    )
  ) {
    return;
  }

  sequence.id = sequence.id || currentSequenceId;
  sequence.name = sequence.name || "New sequence " + store.state.sequences.size;
  sequence.frames = sequence.frames || [];
  sequence.isPlaying = false;
  sequence.currentFrame = sequence.currentFrame || 0;

  initialiseNextFrame(sequence);
  let actionsWithDuration = sequence.frames[nextFrame].filter(
    (action) =>
      action.metadata.duration && !isNaN(parseFloat(action.metadata.duration)),
  );

  let maxFrameActionDuration = actionsWithDuration.reduce((max, action) => {
    return Math.max(max, parseFloat(action.metadata.duration));
  }, 0);
  maxFrameActionDuration =
    maxFrameActionDuration === 0 ? 1 : maxFrameActionDuration;
  sequence.frames[nextFrame].push({
    metadata: { duration: maxFrameActionDuration, type: SEND_DATA },
    data: {
      elementSource: elementSource.id,
      elementDestination: elementDestination.id,
      color: sequence.defaultFrameColor || store.state.defaultFrameColor,
    },
  });

  ensurePreviousFramesInitialized(sequence, sequence.currentFrame);

  if (store.state.autoIncrementFrame) {
    sequence.currentFrame++;
  }

  await store.dispatch("updateSequenceAction", {
    id: currentSequenceId,
    sequence: sequence,
    undoable: true,
  });
};

/**
 * Deletes a sequence with the given ID
 * @param {Object} params - The parameters for sequence deletion
 */
export const deleteSequence = async ({
  sequenceId,
  undoable,
  recreateDefault,
}) => {
  const sequences = new Map(store.state.sequences);
  sequences.delete(sequenceId);
  await store.dispatch("refreshSequencesAction", { sequences, undoable });

  const remainingSequences = Array.from(sequences.values());
  if (
    store.state.currentSequenceId === sequenceId &&
    remainingSequences.length > 0
  ) {
    const lastSequenceId = remainingSequences[remainingSequences.length - 1].id;
    await store.dispatch("updateCurrentSequenceIdAction", {
      sequenceId: lastSequenceId,
    });
  } else if (remainingSequences.length === 0 && recreateDefault) {
    await createSequence({});
  }
};

/**
 * Deletes all sequences
 * @param {Object} params - The parameters for sequence deletion
 */
export const deleteAllSequences = async ({ recreateDefault, undoable }) => {
  for (const sequence of Array.from(store.state.sequences.values())) {
    await deleteSequence({ sequenceId: sequence.id, undoable });
  }
  // create default sequence if none exists
  if (store.state.sequences.size === 0 && recreateDefault) {
    await createSequence({});
  }
};

/**
 * Selects a sequence as the current sequence
 * @param {string} sequenceId - The sequence ID to select
 */
export const selectSequence = async (sequenceId) => {
  if (!store.state.sequences.has(sequenceId)) {
    return;
  }
  await store.dispatch("updateCurrentSequenceIdAction", {
    sequenceId,
  });
};

/**
 * Helper function to check if there's any action after frame 0 for a given element
 * @param {Object} sequence - The sequence object
 * @param {string} elementId - The element ID
 * @param {string} type - The action type
 * @returns {boolean} True if an action is found, false otherwise
 */
const foundActionAfterFrame0ForElement = (sequence, elementId, type) => {
  for (let i = 1; i < sequence.frames.length; i++) {
    const frame = sequence.frames[i];
    for (let j = 0; j < frame.length; j++) {
      const action = frame[j];
      if (
        action.metadata.type === type &&
        action.data.elementId === elementId
      ) {
        return true;
      }
    }
  }
  return false;
};

/**
 * Deletes an imported sequence and all its associated actions from a sequence
 * @param {Object} params - The parameters
 * @param {Object} params.sequence - The sequence containing the imported sequence
 * @param {string} params.importGroupId - The import group ID of the sequence to delete
 * @returns {Promise<Object>} - Result object with success flag and message
 */
export const deleteImportedSequence = async ({ sequence, importGroupId }) => {
  if (!sequence) {
    return { success: false, message: "No sequence provided" };
  }

  if (!importGroupId) {
    return { success: false, message: "No import group ID provided" };
  }

  // Clone the sequence for modification
  const updatedSequence = deepClone(sequence);

  // Find the marker action to get the sequence name for the result message
  let sequenceName = "";
  let markerFound = false;

  // Remove all actions with this import group ID from all frames
  for (let i = 0; i < updatedSequence.frames.length; i++) {
    const frame = updatedSequence.frames[i] || [];

    // Find the marker action for the sequence name
    const markerAction = frame.find(
      (action) =>
        action.metadata.type === IMPORTED_SEQUENCE &&
        action.importData?.importGroupId === importGroupId,
    );

    if (markerAction) {
      markerFound = true;
      sequenceName =
        markerAction.importData?.currentSourceSequenceName ||
        markerAction.importData?.sourceSequenceName ||
        "Unknown";
    }

    // Filter out actions that are part of the imported sequence
    updatedSequence.frames[i] = frame.filter((action) => {
      return !(
        (action.metadata &&
          (action.metadata.type === IMPORTED_SEQUENCE ||
            action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
            action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
            action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
            action.metadata.type === WORKSPACE_ZOOM_OUT_2) &&
          action.importData &&
          action.importData.importGroupId === importGroupId) ||
        (action.importData && action.importData.importGroupId === importGroupId)
      );
    });
  }

  if (!markerFound) {
    return { success: false, message: "Imported sequence not found" };
  }

  // Clean up empty frames
  cleanEmptyFrames(updatedSequence);

  // Update the sequence in the store
  await store.dispatch("updateSequenceAction", {
    id: updatedSequence.id,
    sequence: updatedSequence,
    undoable: true,
  });

  return {
    success: true,
    message: `Imported sequence '${sequenceName}' deleted successfully`,
  };
};

/**
 * Deletes an action from a sequence frame
 * @param {Object} params - The parameters for action deletion
 */
export const deleteSequenceFrameAction = async ({
  sequence,
  frameIndex,
  actionIndex,
  undoable,
}) => {
  if (!sequence) return;

  const frame = sequence.frames[frameIndex];
  if (!frame) return;

  const action = frame[actionIndex];
  if (!action) return;

  const isWaitActionDeleted = action.metadata.type === WAIT;

  // Delete the specific action
  frame.splice(actionIndex, 1);

  // special case for first frame for a non interpolated position action
  if (
    frameIndex === 0 &&
    action.metadata.type === ELEMENT_POSITION &&
    foundActionAfterFrame0ForElement(
      sequence,
      action.data.elementId,
      ELEMENT_POSITION,
    )
  ) {
    await setupElementPositionInitialAction(sequence, action.data.elementId);
    return;
  }
  if (
    frameIndex === 0 &&
    action.metadata.type === VIEWBOX &&
    foundActionAfterFrame0ForElement(sequence, action.data.elementId, VIEWBOX)
  ) {
    await setupInitialViewboxAction(sequence);
    return;
  }
  if (
    frameIndex === 0 &&
    action.metadata.type === ELEMENT_OPACTITY &&
    foundActionAfterFrame0ForElement(
      sequence,
      action.data.elementId,
      ELEMENT_OPACTITY,
    )
  ) {
    await setupElementOpacityInitialAction(sequence, action.data.elementId);
    await updateElementOpacity(sequence, 0, action.data.elementId, 1); // this should be part of setupElementOpacityInitialAction I think
    return;
  }

  // Handle removing previous interpolated actions for elementPosition or viewbox
  if (
    action.metadata.type === ELEMENT_POSITION ||
    action.metadata.type === VIEWBOX ||
    action.metadata.type === ELEMENT_OPACTITY
  ) {
    const elementId = action.data.elementId;

    // Check if there's a next non-interpolated action for this element
    let hasNextNonInterpolatedAction = false;
    for (let i = frameIndex + 1; i < sequence.frames.length; i++) {
      const nextFrame = sequence.frames[i];
      const nextNonInterpolatedAction = nextFrame?.find(
        (a) =>
          (a.data.elementId === elementId ||
            (a.data.position && elementId === "viewbox")) &&
          !a.metadata.interpolated,
      );
      if (nextNonInterpolatedAction) {
        hasNextNonInterpolatedAction = true;
        break;
      }
    }

    // Find previous non-interpolated action for this element or frame 0 interpolated action
    for (let i = frameIndex - 1; i >= 0; i--) {
      const previousFrame = sequence.frames[i];
      let previousNonInterpolatedActionOrFrame0InterpolatedAction =
        previousFrame?.find(
          (a) =>
            (a.data.elementId === elementId ||
              (a.data.position && elementId === "viewbox")) &&
            !a.metadata.interpolated &&
            a.metadata.type === action.metadata.type,
        );
      if (previousNonInterpolatedActionOrFrame0InterpolatedAction) {
        break;
      }
      // Well there should always be at least an interpolated action for first frame for an element, let's find it
      if (i === 0) {
        previousNonInterpolatedActionOrFrame0InterpolatedAction =
          previousFrame?.find(
            (a) =>
              a.data.elementId === elementId ||
              (a.data.position && elementId === "viewbox"),
          );
      }
    }

    // We only update interpolated positions if there's a next and previous non-interpolated action
    if (hasNextNonInterpolatedAction) {
      if (action.metadata.type === ELEMENT_OPACTITY) {
        await updateOpacityFromInterpolationContext(
          sequence,
          frameIndex,
          action.data.elementId,
        );
      } else {
        updateInterpolatedPositions(
          sequence,
          elementId,
          frameIndex,
          null,
          true,
          action.metadata.type,
        );
      }
    }

    // If there's no next non-interpolated action, remove previous interpolated actions
    if (!hasNextNonInterpolatedAction) {
      for (let i = frameIndex - 1; i > 0; i--) {
        const previousFrame = sequence.frames[i];
        const interpolatedActionIndex = previousFrame.findIndex(
          (a) =>
            (a.data.elementId === elementId ||
              (a.data.position && elementId === "viewbox")) &&
            a.metadata.interpolated &&
            a.metadata.type === action.metadata.type,
        );
        if (interpolatedActionIndex !== -1) {
          previousFrame.splice(interpolatedActionIndex, 1);
        } else {
          break;
        }
      }
    }
  }

  // Handle empty frames and frames containing only wait actions
  if (isWaitActionDeleted && frame.length === 0) {
    sequence.frames.splice(frameIndex, 1);
    frameIndex -= 1; // current frameIndex has now changed as it's been deleted
  }
  for (let i = frameIndex; i > 0; i--) {
    if (!sequence.frames[i]) continue;

    if (
      sequence.frames[i].length === 0 ||
      (sequence.frames[i].length === 1 &&
        sequence.frames[i][0] &&
        sequence.frames[i][0].metadata &&
        (sequence.frames[i][0].metadata.type === WAIT ||
          sequence.frames[i][0].metadata.interpolated))
    ) {
      const hasNonEmptyFramesAfter = sequence.frames
        .slice(i + 1)
        .some((f) => f.length > 0);

      if (!hasNonEmptyFramesAfter) {
        sequence.frames.splice(i, 1);
      } else if (sequence.frames[i].length === 0) {
        sequence.frames[i] = [WAIT_ACTION];
      }
    } else {
      break; // Stop if a non-empty or non-wait-only frame is found
    }
  }

  // Update the sequence in the store
  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence,
    undoable,
  });
};

/**
 * Inserts a frame at the specified index in the sequence
 * @param {object} sequence - The sequence
 * @param {number} frameIndex - The index to insert the frame at
 */
export const insertFrame = async (sequence, frameIndex) => {
  if (!sequence) return;

  // Create a new frame with a WAIT action
  const newFrame = [
    {
      metadata: { type: WAIT },
      data: {
        color: sequence.defaultFrameColor || store.state.defaultFrameColor,
      },
    },
  ];

  // Insert the new frame at the specified index
  sequence.frames.splice(frameIndex, 0, newFrame);

  // We need to do this dodgy thing to offset sendData events and bring the ones over current frame back one frame
  // First we get the sendData actions that have been moved forward with frame number = frameIndex
  const actionsIndicesToMove = [];
  for (let i = 0; i < sequence.frames[frameIndex + 1]?.length; i++) {
    if (sequence.frames[frameIndex + 1][i].metadata.type === SEND_DATA) {
      actionsIndicesToMove.push(i);
    }
  }
  for (const index of actionsIndicesToMove) {
    await moveActionToFrame(sequence, frameIndex + 1, index, frameIndex);
  }

  // Handle interpolation for existing elements
  const elementIds = new Set();
  sequence.frames.forEach((frame) =>
    frame.forEach((action) => {
      if (action.data.elementId) {
        elementIds.add(action.data.elementId);
      }
    }),
  );

  for (const elementId of elementIds) {
    // No new position at the inserted frame, just interpolation except for frame 0 where we want to use the default position
    const newPosition = getNewPosition(frameIndex, elementId);
    if (newPosition && frameIndex === 0) {
      await updatePosition({
        sequence,
        frameIndex: 0,
        elementId,
        elementPosition: newPosition,
        type: elementId === "viewbox" ? VIEWBOX : ELEMENT_POSITION,
        interpolated: true,
        undoable: false,
      });
      // Get a fresh copy from the store since it may have been updated
      removeWaitAction(sequence.frames[0]);
      await store.dispatch("updateSequenceAction", {
        id: sequence.id,
        sequence,
        undoable: true,
      });
    } else {
      updateInterpolatedPositions(
        sequence,
        elementId,
        frameIndex,
        newPosition,
        true,
        ELEMENT_POSITION,
      );
      await store.dispatch("updateSequenceAction", {
        id: sequence.id,
        sequence,
        undoable: true,
      });
    }
  }
};

/**
 * Deletes a frame from a sequence
 * @param {object} sequence - The sequence
 * @param {number} frameIndex - The index of the frame to delete
 */
export const deleteFrame = async (sequence, frameIndex) => {
  if (sequence.frames.length < frameIndex + 1) {
    return;
  }
  // get all elements involved in this frame
  const elementIds = [];
  const sendDataActionIndicesNextFrame = [];
  const sendDataActionsCurrentFrame = sequence.frames[frameIndex].filter(
    (action) => action.metadata.type === SEND_DATA,
  );
  const actionsNextFrame = sequence.frames[frameIndex + 1];
  if (actionsNextFrame) {
    for (let i = 0; i < actionsNextFrame.length; i++) {
      const action = actionsNextFrame[i];
      if (action.metadata.type === SEND_DATA) {
        sendDataActionIndicesNextFrame.push(i);
      }
    }
  }
  // delete send data actions on frameIndex + 1 corresponding to the indices saved
  for (let i = sequence.frames[frameIndex + 1]?.length - 1; i >= 0; i--) {
    if (sendDataActionIndicesNextFrame.includes(i)) {
      sequence.frames[frameIndex + 1].splice(i, 1);
    }
  }
  sequence.frames[frameIndex].forEach((action) => {
    if (action.data.elementId) {
      elementIds.push(action.data.elementId);
    }
  });

  //Proper deletion of the actions using the deleteSequenceFrameAction at frameIndex
  for (const action of sequence.frames[frameIndex].filter(
    (action) =>
      action.metadata.type !== SEND_DATA && action.metadata.type !== WAIT,
  )) {
    const index = sequence.frames[frameIndex].indexOf(action);
    await deleteSequenceFrameAction({
      sequence,
      frameIndex,
      actionIndex: index,
    });
  }

  // Get a fresh copy after deletions

  // Actual deletion of the frame
  sequence.frames.splice(frameIndex, 1);

  if (frameIndex === 0) {
    for (const elementId of elementIds) {
      if (
        foundActionAfterFrame0ForElement(sequence, elementId, ELEMENT_POSITION)
      ) {
        await setupElementPositionInitialAction(sequence, elementId);
      } else if (
        foundActionAfterFrame0ForElement(sequence, elementId, ELEMENT_OPACTITY)
      ) {
        await setupElementOpacityInitialAction(sequence, elementId);
      } else if (
        foundActionAfterFrame0ForElement(sequence, elementId, VIEWBOX)
      ) {
        await setupInitialViewboxAction(sequence);
      }
    }
  }
  // Re-insertion of saved send data actions
  for (const action of sendDataActionsCurrentFrame) {
    if (!sequence.frames[frameIndex]) {
      sequence.frames[frameIndex] = [];
    }
    sequence.frames[frameIndex].push({ ...action });
  }

  // Interpolation
  for (const elementId of elementIds) {
    // Get position action for that element and figure out if it is interpolated
    if (!sequence.frames[frameIndex]) {
      continue;
    }
    const positionAction = sequence.frames[frameIndex].find(
      (action) =>
        action.data.elementId === elementId &&
        action.metadata.type === ELEMENT_POSITION &&
        !action.metadata.interpolated,
    );
    let interpolateCurrentFrame = true;
    if (positionAction && !positionAction.metadata.interpolated) {
      interpolateCurrentFrame = false;
    }
    updateInterpolatedPositions(
      sequence,
      elementId,
      frameIndex,
      positionAction?.data?.position,
      interpolateCurrentFrame,
      elementId === "viewbox" ? VIEWBOX : ELEMENT_POSITION,
    );
    await updateOpacityFromInterpolationContext(
      sequence,
      frameIndex - 1,
      elementId,
    );
  }
  updateInterpolatedPositions(
    sequence,
    "viewbox",
    frameIndex - 1,
    null,
    true,
    VIEWBOX,
  );

  // Handle empty remaining frames
  for (let i = frameIndex; i > 0; i--) {
    if (!sequence.frames[i]) {
      continue;
    }
    if (
      sequence.frames[i].length === 0 ||
      (sequence.frames[i].length === 1 &&
        (sequence.frames[i][0].metadata.type === WAIT ||
          sequence.frames[i][0].metadata.interpolated))
    ) {
      const hasNonEmptyFramesAfter = sequence.frames
        .slice(i + 1)
        .some((f) => f.length > 0);

      if (!hasNonEmptyFramesAfter) {
        sequence.frames.splice(i, 1);
      } else if (sequence.frames[i].length === 0) {
        sequence.frames[i] = [WAIT_ACTION];
      }
    } else {
      break; // Stop if a non-empty or non-wait-only frame is found
    }
  }

  // If there are no more frames, we just recreate one with empty array
  if (sequence.frames.length === 0) {
    sequence.frames.push([]);
  }
  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence: sequence,
    undoable: true,
  });
};

/**
 * Moves an action from one frame to another
 * @param {Object} sequence - The sequence object
 * @param {number} frameIndex - The current frame index of the action
 * @param {number} actionIndex - The index of the action in the frame
 * @param {number} newFrameIndex - The new frame index to move the action to
 */
export const moveActionToFrame = async (
  sequence,
  frameIndex,
  actionIndex,
  newFrameIndex,
) => {
  if (!sequence) {
    return;
  }
  const frame = sequence.frames[frameIndex];
  if (!frame) {
    return;
  }
  const action = { ...frame[actionIndex] };
  if (!action) {
    return;
  }

  // Ensure the target frame exists
  if (!sequence.frames[newFrameIndex]) {
    sequence.frames[newFrameIndex] = [];
  }

  sequence.frames[newFrameIndex].push(action);
  removeWaitAction(sequence.frames[newFrameIndex]);
  await deleteSequenceFrameAction({
    sequence,
    frameIndex,
    actionIndex,
    undoable: true,
  });
  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence,
    undoable: true,
  });
};

/**
 * Updates the opacity of an element in a sequence frame
 * @param sequence - The sequence
 * @param {number} frameIndex - The frame index
 * @param {string} elementId - The element ID
 * @param {number} opacity - The opacity value (0-1)
 */
export const updateElementOpacity = async (
  sequence,
  frameIndex,
  elementId,
  opacity,
) => {
  if (!sequence) return;

  sequence.frames[frameIndex] = sequence.frames[frameIndex] || [];
  const frame = sequence.frames[frameIndex];

  const existingActionIndex = frame.findIndex(
    (action) =>
      action.data.elementId === elementId &&
      action.metadata.type === ELEMENT_OPACTITY,
  );

  if (existingActionIndex !== -1) {
    // Update the existing opacity value
    frame[existingActionIndex].data.opacity = opacity;
    frame[existingActionIndex].metadata.interpolated = false;
  } else {
    // Add a new opacity action for the element in this frame
    addElementOpacityAction(frame, elementId, opacity, false);
  }

  // special case where we are setting an opacity to 0 and frame 0 wasn't set with any opacity and no opacity in between
  if (
    !sequence.frames[0]?.find(
      (action) =>
        action.data.elementId === elementId &&
        action.metadata.type === ELEMENT_OPACTITY &&
        action.data.opacity != null,
    )
  ) {
    let foundOpacityActionInBetween = false;
    for (let i = frameIndex - 1; i > 0; i--) {
      if (
        sequence.frames[i]?.find(
          (action) =>
            action.data.elementId === elementId &&
            action.metadata.type === ELEMENT_OPACTITY &&
            action.data.opacity != null,
        )
      ) {
        foundOpacityActionInBetween = true;
        break;
      }
    }
    if (!foundOpacityActionInBetween) {
      sequence.frames[0] = sequence.frames[0] || [];
      addElementOpacityAction(sequence.frames[0], elementId, 1, false);
    }
  }

  ensurePreviousFramesInitialized(sequence, frameIndex);

  // Update previous and future frames for visibility interpolation
  await updateOpacityFromInterpolationContext(sequence, frameIndex, elementId);

  // Update the sequence in the store
  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence,
    undoable: true,
  });
};

/**
 * Updates the position of an element or viewbox in a sequence frame
 * @param {Object} params - Parameters for position update
 */
export const updatePosition = async ({
  sequence,
  frameIndex,
  elementId,
  elementPosition,
  type,
  interpolated = false,
  undoable = false,
}) => {
  if (!sequence) return;

  let newPosition;
  if (type === VIEWBOX) {
    newPosition = {
      x: draw.viewbox().x,
      y: draw.viewbox().y,
      width: draw.viewbox().width,
      height: draw.viewbox().height,
    };
    if (!sequence.dimensions) {
      sequence.dimensions = {};
    }
    sequence.dimensions.windowWidth = window.innerWidth;
    sequence.dimensions.windowHeight = window.innerHeight;
  } else {
    newPosition = elementPosition;
  }

  // Ensure the frame exists
  if (!sequence.frames[frameIndex]) {
    sequence.frames[frameIndex] = [];
  }

  const frame = sequence.frames[frameIndex];

  let actionsWithDuration = frame.filter(
    (action) =>
      action.metadata.duration && !isNaN(parseFloat(action.metadata.duration)),
  );

  let maxFrameActionDuration = actionsWithDuration.reduce((max, action) => {
    return Math.max(max, parseFloat(action.metadata.duration));
  }, 0);
  maxFrameActionDuration =
    maxFrameActionDuration === 0 ? 1 : maxFrameActionDuration;
  // Find or create the action for the element position
  let action = frame.find(
    (action) =>
      action.data.elementId === elementId &&
      (action.metadata.type === ELEMENT_POSITION ||
        action.metadata.type === VIEWBOX),
  );

  // Check if this frame belongs to an imported sequence
  const importData = getImportDataForFrame(sequence, frameIndex);

  if (!action) {
    action = {
      metadata: {
        type,
        interpolated,
        duration: maxFrameActionDuration, // we don't want to slow down animation when adding new action to existing frames
      },
      data: { elementId, position: newPosition },
    };

    // If this is a viewbox action and the frame belongs to an imported sequence,
    // add the importData to make it part of the imported sequence
    if (type === VIEWBOX && importData) {
      action.importData = { ...importData };
    }

    frame.push(action);
  } else {
    action.data.position = newPosition;
    action.metadata.interpolated = interpolated;

    // If this is a viewbox action and the frame belongs to an imported sequence,
    // ensure it has the importData to make it part of the imported sequence
    if (type === VIEWBOX && importData && !action.importData) {
      action.importData = { ...importData };
    }
  }

  // Update other frames' interpolation
  updateInterpolatedPositions(
    sequence,
    elementId,
    frameIndex,
    newPosition,
    interpolated,
    type,
  );

  // Update the sequence in the store
  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence: sequence,
    undoable,
  });
};

/**
 * Updates element position with lazy loading of initial actions
 * @param {Object} params - The parameters for position update
 */
export const updateElementPositionWithLazyLoading = async ({
  sequence,
  frameIndex,
  elementId,
  elementPosition,
  optionalFunction,
  undoable,
}) => {
  // Lazy loading
  await setupElementPositionInitialAction(sequence, elementId);

  // function that applies to elementId to execute, like showItemPositions
  if (optionalFunction) {
    optionalFunction(elementId);
  }

  // Actual position update
  await updatePosition({
    sequence,
    frameIndex,
    elementId,
    elementPosition,
    type: ELEMENT_POSITION,
    interpolated: false,
    undoable,
  });
};

/**
 * Updates viewbox position with lazy loading of initial actions
 * @param {Object} params - The parameters for position update
 */
export const updateViewboxPositionWithLazyLoading = async ({
  sequence,
  frameIndex,
  elementPosition,
  undoable,
}) => {
  // Lazy loading
  await setupInitialViewboxAction(sequence);
  // Actual position update
  await updatePosition({
    sequence,
    frameIndex,
    elementId: "viewbox",
    elementPosition,
    type: VIEWBOX,
    interpolated: false,
    undoable,
  });
};

/**
 * Gets a new position for an element or viewbox in the first frame
 * @param {number} frameIndex - The frame index
 * @param {string} elementId - The element ID
 * @returns {Object|null} The position or null
 */
function getNewPosition(frameIndex, elementId) {
  return frameIndex === 0
    ? elementId === "viewbox"
      ? getPositionFromCurrentView()
      : getPositionFromStoredElement(
          store.state.allSimpleElements.get(elementId),
        )
    : null;
}

/**
 * Checks if a frame belongs to an imported sequence and returns the importData
 * @param {Object} sequence - The sequence object
 * @param {number} frameIndex - The frame index to check
 * @returns {Object|null} The importData object or null if not an imported sequence frame
 */
function getImportDataForFrame(sequence, frameIndex) {
  if (!sequence || !sequence.frames[frameIndex]) return null;

  const frame = sequence.frames[frameIndex];

  // Check if this frame contains any actions from an imported sequence
  const actionWithImportData = frame
    .filter((action) => action.metadata.type !== IMPORTED_SEQUENCE)
    .find((action) => action.importData);

  if (actionWithImportData && actionWithImportData.importData) {
    return actionWithImportData.importData;
  }

  return null;
}

/**
 * Sets up initial position action for an element in the first frame
 * @param {Object} sequence - The sequence object
 * @param {string} elementId - The element ID
 */
export async function setupElementPositionInitialAction(sequence, elementId) {
  if (elementId === "viewbox") {
    return;
  }
  const element = store.state.allSimpleElements.get(elementId);
  if (!sequence.frames[0]) {
    sequence.frames.push([]);
  }
  const firstFramePositionAction = sequence.frames[0].find((action) => {
    return (
      action.data.elementId === elementId &&
      action.metadata.type === ELEMENT_POSITION &&
      !action.metadata.interpolated
    );
  });
  if (!firstFramePositionAction) {
    await updatePosition({
      sequence,
      frameIndex: 0,
      elementId,
      elementPosition: getPositionFromStoredElement(element),
      type: ELEMENT_POSITION,
      interpolated: true,
      undoable: false,
    });
    await store.dispatch("updateSequenceAction", {
      id: sequence.id,
      sequence: sequence,
      undoable: true,
    });
  }
}

/**
 * Sets up initial opacity action for an element in the first frame
 * @param {object} sequence - The sequence
 * @param {string} elementId - The element ID
 */
async function setupElementOpacityInitialAction(sequence, elementId) {
  if (!sequence.frames[0]) {
    sequence.frames.push([]);
  }
  const firstFrameOpacityAction = sequence.frames[0].find((action) => {
    return (
      action.data.elementId === elementId &&
      action.metadata.type === ELEMENT_OPACTITY
    );
  });
  if (!firstFrameOpacityAction) {
    addElementOpacityAction(sequence.frames[0], elementId, 1, true);
  }
  await store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence: sequence,
    undoable: true,
  });
}

/**
 * Sets up initial viewbox action in the first frame
 * @param {Object} sequence - The sequence object
 */
async function setupInitialViewboxAction(sequence) {
  if (!sequence.frames[0]) {
    sequence.frames.push([]);
  }
  if (!sequence.frames[0].some((action) => action.metadata.type === VIEWBOX)) {
    const initialViewbox = draw.viewbox();
    sequence.frames[0].push({
      metadata: {
        type: VIEWBOX,
        interpolated: false,
        duration: 1,
      },
      data: {
        elementId: "viewbox",
        position: {
          x: initialViewbox.x,
          y: initialViewbox.y,
          width: initialViewbox.width,
          height: initialViewbox.height,
        },
      },
    });
  }
}

/**
 * Updates opacity actions in frames based on interpolation context
 * @param {Object} sequence - The sequence object
 * @param {number} frameIndex - The frame index
 * @param {string} elementId - The element ID
 */
async function updateOpacityFromInterpolationContext(
  sequence,
  frameIndex,
  elementId,
) {
  // Find the current frame
  const currentFrame = sequence.frames[frameIndex];

  if (!currentFrame) {
    return;
  }

  // Check if there is an opacity action in the current frame
  const currentOpacityAction = currentFrame.find(
    (action) =>
      action.data?.elementId === elementId &&
      action.metadata.type === ELEMENT_OPACTITY,
  );

  let previousOpacity = null;
  let nextOpacity = null;
  let previousFrameIndex = null;
  let nextFrameIndex = null;
  let hasNextOpacityAction = false;

  // Find the direct previous frame with a non-interpolated opacity action
  let startingFrameNumberInLoop = Math.max(frameIndex - 1, 0);
  for (let i = startingFrameNumberInLoop; i >= 0; i--) {
    sequence.frames[i] = sequence.frames[i] || [WAIT_ACTION];
    const previousFrame = sequence.frames[i];
    const previousOpacityAction = previousFrame?.find(
      (action) =>
        action.data.elementId === elementId &&
        action.metadata.type === ELEMENT_OPACTITY &&
        (!action.metadata.interpolated || i === 0),
    );

    if (previousOpacityAction) {
      previousOpacity = previousOpacityAction.data.opacity;
      previousFrameIndex = i;
      break;
    }
  }

  // Find the next direct frame with a non-interpolated opacity action
  for (let i = frameIndex + 1; i < sequence.frames.length; i++) {
    const nextFrame = sequence.frames[i] || [];
    const nextOpacityAction = nextFrame.find(
      (action) =>
        action.data?.elementId === elementId &&
        action.metadata?.type === ELEMENT_OPACTITY &&
        !action.metadata?.interpolated,
    );

    if (nextOpacityAction) {
      nextFrameIndex = i;
      nextOpacity = nextOpacityAction.data.opacity;
      hasNextOpacityAction = true;
      break;
    }
  }

  if (
    nextFrameIndex == null &&
    previousFrameIndex == null &&
    !currentOpacityAction
  ) {
    return;
  }

  if (previousFrameIndex == null) {
    previousFrameIndex = -1;
  }

  const currentOpacity = currentOpacityAction?.data.opacity;

  // Fill up all frames between the previous and the current with the value of the previous opacity
  const opacityToSet =
    previousOpacity != null
      ? previousOpacity
      : currentOpacity != null
        ? currentOpacity
        : nextOpacity; // Special case where the first frame was not even set
  for (let i = previousFrameIndex + 1; i < frameIndex; i++) {
    sequence.frames[i] = sequence.frames[i] || [];
    const frame = sequence.frames[i];
    const opacityAction = frame?.find(
      (action) =>
        action.data.elementId === elementId &&
        action.metadata.type === ELEMENT_OPACTITY,
    );

    if (!opacityAction) {
      addElementOpacityAction(frame, elementId, opacityToSet, true);
    } else {
      opacityAction.data.opacity = opacityToSet;
    }
  }

  if (!currentOpacityAction) {
    addElementOpacityAction(currentFrame, elementId, opacityToSet, true);
  }

  const newCurrentOpacity =
    currentOpacity != null ? currentOpacity : previousOpacity;

  // Fill up all frames between the current and the next with the value of the current opacity
  if (opacityToSet !== null && nextFrameIndex != null) {
    for (let i = frameIndex + 1; i < nextFrameIndex; i++) {
      const frame = sequence.frames[i];
      const opacityAction = frame.find(
        (action) =>
          action.data?.elementId === elementId &&
          action.metadata.type === ELEMENT_OPACTITY,
      );

      if (!opacityAction && hasNextOpacityAction) {
        addElementOpacityAction(frame, elementId, newCurrentOpacity, true);
      } else if (opacityAction) {
        opacityAction.data.opacity = newCurrentOpacity;
      }
    }
  }
  // Preliminary cautious check: the first frame should always have an opacity action at least interpolated to true with value of 1
  await setupElementOpacityInitialAction(sequence, elementId);
}

/**
 * Creates a new sequence with the given parameters or defaults
 * @param {string} id - The sequence ID
 * @param {string} name - The sequence name
 * @param {Array} frames - The frames array
 * @param {boolean} isPlaying - Whether the sequence is playing
 * @param {number} currentFrame - The current frame index
 * @returns {Object} The initialized sequence
 */
const initialiseSequence = (
  id,
  name,
  frames,
  isPlaying,
  currentFrame,
  parentWorkspaceId,
) => {
  const sequenceId = id || uuidv4();

  parentWorkspaceId = parentWorkspaceId || store.state.currentWorkspaceId;
  return {
    id: sequenceId,
    name: name || "New sequence " + store.state.sequences.size,
    frames: frames || [[]],
    isPlaying: isPlaying || false,
    currentFrame: currentFrame || 0,
    dimensions: {
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
    },
    parentWorkspaceId: parentWorkspaceId,
  };
};

/**
 * Initializes the next frame in a sequence if it doesn't exist
 * @param {Object} sequence - The sequence object
 */
const initialiseNextFrame = (sequence) => {
  const nextFrame = sequence.currentFrame + 1;
  sequence.frames[nextFrame] = sequence.frames[nextFrame] || [];
  if (
    sequence.frames[nextFrame].every((action) => action.metadata.type === WAIT)
  ) {
    sequence.frames[nextFrame] = [];
  }
};

/**
 * Ensures that all frames up to frameIndex exist
 * @param {Object} sequence - The sequence object
 * @param {number} frameIndex - The frame index
 */
const ensurePreviousFramesInitialized = (sequence, frameIndex) => {
  for (let i = 0; i <= frameIndex; i++) {
    if (isEmptyObject(sequence.frames[i])) {
      sequence.frames[i] = [
        {
          metadata: { type: WAIT },
          data: {},
        },
      ];
    }
  }
};

/**
 * Adds an opacity action to a frame
 * @param {Array} frame - The frame array
 * @param {string} elementId - The element ID
 * @param {number} opacity - The opacity value
 * @param {boolean} interpolated - Whether the action is interpolated
 */
const addElementOpacityAction = (frame, elementId, opacity, interpolated) => {
  removeWaitAction(frame);
  let actionsWithDuration = frame.filter(
    (action) =>
      action.metadata.duration && !isNaN(parseFloat(action.metadata.duration)),
  );

  let maxFrameActionDuration = actionsWithDuration.reduce((max, action) => {
    return Math.max(max, parseFloat(action.metadata.duration));
  }, 0);
  maxFrameActionDuration =
    maxFrameActionDuration === 0 ? 1 : maxFrameActionDuration;
  frame.push({
    metadata: {
      duration: maxFrameActionDuration,
      type: ELEMENT_OPACTITY,
      interpolated,
    },
    data: { elementId, opacity },
  });
};

/**
 * Removes wait actions from a frame
 * @param {Array} frame - The frame array
 */
function removeWaitAction(frame) {
  // Find and remove wait action from the frame
  const waitIndex = frame.findIndex(
    (action) => action?.metadata?.type === WAIT,
  );
  if (waitIndex !== -1) {
    frame.splice(waitIndex, 1); // Remove the wait action
  }
}

/**
 * Interpolates a position between two frames
 * @param {Array} frames - The frames array
 * @param {number} frameIndex - The frame index
 * @param {string} elementId - The element ID
 * @param {number} prevFrameIndex - The previous frame index
 * @param {Object} prevPosition - The previous position
 * @param {Object} nextPosition - The next position
 * @param {number} totalFrames - The total number of frames
 * @param {string} type - The action type
 */
function interpolate(
  frames,
  frameIndex,
  elementId,
  prevFrameIndex,
  prevPosition,
  nextPosition,
  totalFrames,
  type,
  isImportedSequenceFrame = false,
  currentFrameImportData = null,
) {
  if (!frames[frameIndex]) {
    frames[frameIndex] = [];
  }
  const frame = frames[frameIndex];
  removeWaitAction(frame);

  // For viewbox actions, check if this frame belongs to the same imported sequence context
  // as the current frame we're processing
  if (type === VIEWBOX) {
    const frameImportData = frame.find((a) => a.importData)?.importData;
    const frameIsImported = !!frameImportData;

    // If the contexts don't match (one is imported, one is not), skip interpolation
    if (isImportedSequenceFrame !== frameIsImported) {
      return; // Don't interpolate between different contexts
    }

    // If both are imported, make sure they're from the same imported sequence
    if (
      isImportedSequenceFrame &&
      frameIsImported &&
      frameImportData.importGroupId !== currentFrameImportData.importGroupId
    ) {
      return; // Don't interpolate between different imported sequences
    }
  }

  let actionsWithDuration = frame.filter(
    (action) =>
      action.metadata.duration && !isNaN(parseFloat(action.metadata.duration)),
  );

  let maxFrameActionDuration = actionsWithDuration.reduce((max, action) => {
    return Math.max(max, parseFloat(action.metadata.duration));
  }, 0);
  maxFrameActionDuration =
    maxFrameActionDuration === 0 ? 1 : maxFrameActionDuration;

  // When finding an existing action, make sure it matches the imported/non-imported context
  let action = frame.find(
    (action) =>
      action.data.elementId === elementId &&
      action.metadata.interpolated &&
      action.metadata.type === type &&
      // For viewbox actions, ensure we're in the same context (imported or non-imported)
      (type !== VIEWBOX ||
        (isImportedSequenceFrame && action.importData) ||
        (!isImportedSequenceFrame && !action.importData)),
  );

  if (!action) {
    action = {
      metadata: {
        type,
        interpolated: true,
        duration: maxFrameActionDuration,
      },
      data: { elementId, position: {} },
    };

    // If this is a viewbox action and the frame belongs to an imported sequence,
    // add the importData to make it part of the imported sequence
    if (type === VIEWBOX && isImportedSequenceFrame && currentFrameImportData) {
      action.importData = { ...currentFrameImportData };
    }

    frame.push(action);
  }

  // If this is an existing action, ensure it has importData if needed
  if (
    type === VIEWBOX &&
    isImportedSequenceFrame &&
    currentFrameImportData &&
    !action.importData
  ) {
    action.importData = { ...currentFrameImportData };
  }

  const frameDiff = frameIndex - prevFrameIndex;
  action.data.position.x =
    prevPosition.x +
    ((nextPosition.x - prevPosition.x) * frameDiff) / totalFrames;
  action.data.position.y =
    prevPosition.y +
    ((nextPosition.y - prevPosition.y) * frameDiff) / totalFrames;
  if (action.metadata.type === VIEWBOX) {
    // if condition temporarily until we unify with element (position.width or .height doesn't make sense
    action.data.position.width =
      prevPosition.width +
      ((nextPosition.width - prevPosition.width) * frameDiff) / totalFrames;
    action.data.position.height =
      prevPosition.height +
      ((nextPosition.height - prevPosition.height) * frameDiff) / totalFrames;
  }
}

/**
 * Updates interpolated positions in frames
 * @param {Object} sequence - The sequence object
 * @param {string} elementId - The element ID
 * @param {number} frameIndex - The frame index
 * @param {Object} newPosition - The new position
 * @param {boolean} interpolateCurrentFrame - Whether to interpolate the current frame
 * @param {string} type - The action type
 */
const updateInterpolatedPositions = (
  sequence,
  elementId,
  frameIndex,
  newPosition,
  interpolateCurrentFrame = false,
  type,
) => {
  const frames = sequence.frames;

  // For viewbox actions, we need to check if we're in an imported sequence context
  // to avoid interpolation between imported and non-imported actions
  const isViewboxAction = type === VIEWBOX;
  const currentFrameImportData = isViewboxAction
    ? getImportDataForFrame(sequence, frameIndex)
    : null;
  const isImportedSequenceFrame = !!currentFrameImportData;

  let prevPosition = null,
    nextPosition = null;
  let prevFrameIndex = -1,
    nextFrameIndex = -1;

  // Find previous non-interpolated position
  // By default we pick frame 0 if the action is there
  let actionFrame0 = frames[0]?.find(
    (action) =>
      action.data.elementId === elementId &&
      action.metadata.type === type &&
      // For viewbox actions, ensure we're in the same context (imported or non-imported)
      (!isViewboxAction || !isImportedSequenceFrame || action.importData),
  );

  if (actionFrame0) {
    // For viewbox actions, only use frame 0 if it's in the same context
    if (
      !isViewboxAction ||
      (isImportedSequenceFrame && actionFrame0.importData) ||
      (!isImportedSequenceFrame && !actionFrame0.importData)
    ) {
      prevPosition = actionFrame0.data.position;
      prevFrameIndex = 0;
    }
  }

  for (let i = frameIndex - 1; i >= 0; i--) {
    const frame = frames[i];
    const action = frame?.find(
      (action) =>
        action.data.elementId === elementId &&
        action.metadata.type === type &&
        action.metadata.interpolated === false &&
        // For viewbox actions, ensure we're in the same context (imported or non-imported)
        (!isViewboxAction ||
          (isImportedSequenceFrame && action.importData) ||
          (!isImportedSequenceFrame && !action.importData)),
    );
    if (action) {
      prevPosition = action.data.position;
      prevFrameIndex = i;
      break;
    }
  }

  // Find next non-interpolated position
  for (let i = frameIndex + 1; i < frames.length; i++) {
    const frame = frames[i];
    const action = frame?.find(
      (action) =>
        action.data.elementId === elementId &&
        !action.metadata.interpolated &&
        action.metadata.type === type &&
        // For viewbox actions, ensure we're in the same context (imported or non-imported)
        (!isViewboxAction ||
          (isImportedSequenceFrame && action.importData) ||
          (!isImportedSequenceFrame && !action.importData)),
    );
    if (action) {
      nextPosition = action.data.position;
      nextFrameIndex = i;
      break;
    }
  }

  // For viewbox actions, we only want to interpolate within the same context (imported or non-imported)
  // If we're in an imported sequence frame, we don't want to interpolate with non-imported actions
  if (isViewboxAction && isImportedSequenceFrame) {
    // For imported sequence viewbox actions, we only interpolate within the imported sequence
    // We don't interpolate with actions outside the imported sequence
    if (interpolateCurrentFrame && prevPosition && nextPosition) {
      const totalFrames = nextFrameIndex - prevFrameIndex;
      for (let i = prevFrameIndex + 1; i < nextFrameIndex; i++) {
        // Only interpolate if the frame is part of the same imported sequence
        const frameImportData = getImportDataForFrame(sequence, i);
        if (
          frameImportData &&
          frameImportData.importGroupId === currentFrameImportData.importGroupId
        ) {
          interpolate(
            frames,
            i,
            elementId,
            prevFrameIndex,
            prevPosition,
            nextPosition,
            totalFrames,
            type,
            isImportedSequenceFrame,
            frameImportData,
          );
        }
      }
    } else {
      if (prevPosition && (newPosition || nextPosition)) {
        const totalFrames = frameIndex - prevFrameIndex;
        for (let i = prevFrameIndex + 1; i < frameIndex; i++) {
          // Only interpolate if the frame is part of the same imported sequence
          const frameImportData = getImportDataForFrame(sequence, i);
          if (
            frameImportData &&
            frameImportData.importGroupId ===
              currentFrameImportData.importGroupId
          ) {
            interpolate(
              frames,
              i,
              elementId,
              prevFrameIndex,
              prevPosition,
              newPosition || nextPosition,
              totalFrames,
              type,
              isImportedSequenceFrame,
              frameImportData,
            );
          }
        }
      }
      if (nextPosition && (newPosition || prevPosition)) {
        const totalFrames = nextFrameIndex - frameIndex;
        for (let i = frameIndex + 1; i < nextFrameIndex; i++) {
          // Only interpolate if the frame is part of the same imported sequence
          const frameImportData = getImportDataForFrame(sequence, i);
          if (
            frameImportData &&
            frameImportData.importGroupId ===
              currentFrameImportData.importGroupId
          ) {
            interpolate(
              frames,
              i,
              elementId,
              frameIndex,
              newPosition || prevPosition,
              nextPosition,
              totalFrames,
              type,
              isImportedSequenceFrame,
              frameImportData,
            );
          }
        }
      }
    }
  } else {
    // Regular interpolation for non-viewbox actions or non-imported viewbox actions
    if (interpolateCurrentFrame && prevPosition && nextPosition) {
      const totalFrames = nextFrameIndex - prevFrameIndex;
      for (let i = prevFrameIndex + 1; i < nextFrameIndex; i++) {
        // For viewbox actions, skip frames that belong to imported sequences
        if (!isViewboxAction || !getImportDataForFrame(sequence, i)) {
          interpolate(
            frames,
            i,
            elementId,
            prevFrameIndex,
            prevPosition,
            nextPosition,
            totalFrames,
            type,
            isImportedSequenceFrame,
            currentFrameImportData,
          );
        }
      }
    } else {
      if (prevPosition && (newPosition || nextPosition)) {
        const totalFrames = frameIndex - prevFrameIndex;
        for (let i = prevFrameIndex + 1; i < frameIndex; i++) {
          // For viewbox actions, skip frames that belong to imported sequences
          if (!isViewboxAction || !getImportDataForFrame(sequence, i)) {
            if (newPosition || nextPosition) {
              interpolate(
                frames,
                i,
                elementId,
                prevFrameIndex,
                prevPosition,
                newPosition || nextPosition,
                totalFrames,
                type,
                isImportedSequenceFrame,
                currentFrameImportData,
              );
            }
          }
        }
      }
      if (nextPosition && (newPosition || prevPosition)) {
        const totalFrames = nextFrameIndex - frameIndex;
        for (let i = frameIndex + 1; i < nextFrameIndex; i++) {
          // For viewbox actions, skip frames that belong to imported sequences
          if (!isViewboxAction || !getImportDataForFrame(sequence, i)) {
            interpolate(
              frames,
              i,
              elementId,
              frameIndex,
              newPosition || prevPosition,
              nextPosition,
              totalFrames,
              type,
              isImportedSequenceFrame,
              currentFrameImportData,
            );
          }
        }
      }
    }
  }
};

/**
 * Updates initial sequences after element update in creation mode
 * @param {Object} params - The parameters
 */
export const updateInitialSequencesAfterElementUpdateInCreationMode = async ({
  element,
  undoable,
}) => {
  if (store.state.diagramMode !== diagramMode.creationMode) {
    return;
  }
  for (const sequence of store.state.sequences.values()) {
    const sequenceCopy = deepClone(sequence);
    const foundActionForFrame0 = sequenceCopy.frames[0]?.find(
      (action) =>
        action.data.elementId === element.id &&
        action.metadata.type === ELEMENT_POSITION,
    );
    const foundNonInterpolatedActionForElement = sequenceCopy.frames.some(
      (frame) =>
        frame.some(
          (action) =>
            action.data.elementId === element.id &&
            action.metadata.type === ELEMENT_POSITION &&
            !action.metadata.interpolated,
        ),
    );
    if (foundActionForFrame0 && !foundActionForFrame0.metadata.interpolated) {
      // if there is an interpolated action, we ignore
      continue;
    }
    if (!foundNonInterpolatedActionForElement) {
      // Now if there is no manually set action in any frame, we ignore
      continue;
    }
    await updatePosition({
      sequence: sequenceCopy,
      frameIndex: 0,
      elementId: element.id,
      elementPosition: getPositionFromStoredElement(element),
      type: ELEMENT_POSITION,
      interpolated: true,
      undoable,
    });
  }
};

/**
 * Repairs a sequence by cleaning up invalid actions
 * @param {string} sequenceId - The sequence ID
 */
export const repairSequence = (sequenceId) => {
  if (!sequenceId) {
    return;
  }
  const sequence = deepClone(store.state.sequences.get(sequenceId));

  // Remove all actions where the element doesn't exist
  sequence.frames?.forEach((frame, frameIndex) => {
    // Iterate backwards to safely remove items
    for (let actionIndex = frame.length - 1; actionIndex >= 0; actionIndex--) {
      const action = frame[actionIndex];

      if (
        action.metadata.type === SEND_DATA ||
        action.metadata.type === VIEWBOX ||
        action.metadata.type === IMPORTED_SEQUENCE ||
        (action.importData && action.importData.importGroupId) // Preserve imported actions
      ) {
        continue; // these actions are not problematic
      }

      if (!store.state.allSimpleElements.get(action.data.elementId)) {
        sequence.frames[frameIndex].splice(actionIndex, 1);
      }
    }
  });

  // Clean frame 0
  const frame0 = sequence.frames[0];
  // Iterate backwards over frame 0
  for (let index = frame0.length - 1; index >= 0; index--) {
    const action = frame0[index];

    if (
      action.metadata.type === SEND_DATA ||
      action.metadata.type === IMPORTED_SEQUENCE ||
      (action.importData && action.importData?.importGroupId) // Preserve imported actions
    ) {
      continue; // these actions are not problematic
    }

    const elementId =
      action.data.elementId ||
      action.data.elementSource ||
      action.data.elementDestination;

    // Check for interpolated actions that have no further actions for this element
    if (
      action.metadata.type === ELEMENT_POSITION &&
      action.metadata.interpolated &&
      !foundActionAfterFrame0ForElement(sequence, elementId, ELEMENT_POSITION)
    ) {
      frame0.splice(index, 1);
      continue;
    }

    if (
      action.metadata.type === ELEMENT_OPACTITY &&
      action.metadata.interpolated &&
      !foundActionAfterFrame0ForElement(sequence, elementId, ELEMENT_OPACTITY)
    ) {
      frame0.splice(index, 1);
      continue;
    }

    if (
      action.metadata.type === VIEWBOX &&
      action.metadata.interpolated &&
      !foundActionAfterFrame0ForElement(sequence, "viewbox", VIEWBOX)
    ) {
      frame0.splice(index, 1);
    }
  }

  // Dispatch update after cleaning frame 0
  store.dispatch("updateSequenceAction", {
    id: sequence.id,
    sequence: sequence,
    undoable: false,
  });
};

// If the last frame only has only wait actions or is empty, we delete the action and loop backwards until frame 0
export const cleanEmptyFrames = (sequence) => {
  if (!sequence) {
    return;
  }
  for (let i = sequence.frames.length - 1; i >= 0; i--) {
    if (!sequence.frames[i]) continue;
    if (
      sequence.frames[i].length === 0 ||
      (sequence.frames[i].length === 1 &&
        sequence.frames[i][0] &&
        sequence.frames[i][0].metadata &&
        sequence.frames[i][0].metadata.type === WAIT)
    ) {
      sequence.frames.splice(i, 1);
    } else {
      break; // Stop if a non-empty or non-wait-only frame is found
    }
  }
  if (sequence.frames.length === 0) {
    sequence.frames.push([]);
  }
};

const WAIT_ACTION = {
  metadata: {
    type: WAIT,
  },
  data: {},
};

/**
 * Removes all actions related to an element from sequences in a workspace
 * @param {string} elementId - The ID of the element to remove actions for
 * @param {string} workspaceId - The ID of the workspace containing the sequences
 * @returns {Promise<Array>} Array of removed actions
 */
export const removeElementActionsFromSequences = async (
  elementId,
  workspaceId,
) => {
  const removedActions = [];

  // Get all sequences in the workspace
  const sequences = Array.from(store.state.sequences.values()).filter(
    (sequence) =>
      !sequence.parentWorkspaceId || // Include sequences without parentWorkspaceId
      sequence.parentWorkspaceId === workspaceId ||
      (Array.isArray(sequence.parentWorkspaceId) &&
        sequence.parentWorkspaceId.includes(workspaceId)),
  );

  for (const sequence of sequences) {
    let sequenceModified = false;
    const updatedSequence = deepClone(sequence);

    // Process each frame in the sequence
    for (
      let frameIndex = 0;
      frameIndex < updatedSequence.frames.length;
      frameIndex++
    ) {
      const frame = updatedSequence.frames[frameIndex];
      if (!frame) continue;

      // Find actions related to the element
      const actionsToRemove = [];
      frame.forEach((action, actionIndex) => {
        if (
          action.data?.elementId === elementId ||
          action.data?.elementSource === elementId ||
          action.data?.elementDestination === elementId
        ) {
          actionsToRemove.push({ action, actionIndex });
        }
      });

      // Remove actions and store them for potential recreation
      if (actionsToRemove.length > 0) {
        // Sort in reverse order to avoid index shifting when removing
        actionsToRemove.sort((a, b) => b.actionIndex - a.actionIndex);

        for (const { action, actionIndex } of actionsToRemove) {
          // Store the removed action with its context
          removedActions.push({
            action: deepClone(action),
            frameIndex,
            sequenceId: sequence.id,
          });

          // Remove the action from the frame
          frame.splice(actionIndex, 1);
        }

        sequenceModified = true;
      }
    }

    // Update the sequence in the store if it was modified
    if (sequenceModified) {
      // Clean up empty frames
      cleanEmptyFrames(updatedSequence);

      await store.dispatch("updateSequenceAction", {
        id: updatedSequence.id,
        sequence: updatedSequence,
        undoable: true,
      });
    }
  }

  return removedActions;
};

/**
 * Recreates actions for an element in a new workspace
 * @param {string} elementId - The ID of the element
 * @param {string} targetWorkspaceId - The ID of the target workspace
 * @param {Array} actions - Array of actions to recreate
 * @returns {Promise<void>}
 */
export const recreateElementActionsInWorkspace = async (
  elementId,
  targetWorkspaceId,
  actions,
) => {
  if (!actions || actions.length === 0) return;
  // Group actions by sequence
  const actionsBySequence = {};

  for (const actionInfo of actions) {
    if (!actionsBySequence[actionInfo.sequenceId]) {
      actionsBySequence[actionInfo.sequenceId] = [];
    }
    actionsBySequence[actionInfo.sequenceId].push(actionInfo);
  }

  // Process each sequence
  for (const sequenceId in actionsBySequence) {
    // Check if a corresponding sequence exists in the target workspace
    let targetSequence = Array.from(store.state.sequences.values()).find(
      (seq) =>
        seq.parentWorkspaceId === targetWorkspaceId ||
        (Array.isArray(seq.parentWorkspaceId) &&
          seq.parentWorkspaceId.includes(targetWorkspaceId)),
    );

    // If no sequence exists in the target workspace, create one
    if (!targetSequence) {
      targetSequence = await createSequence({
        name: `Sequence for ${targetWorkspaceId}`,
        parentWorkspaceId: targetWorkspaceId,
        undoable: true,
      });
    }

    // Clone the target sequence for modification
    const updatedSequence = deepClone(targetSequence);

    // Add actions to the target sequence
    for (const actionInfo of actionsBySequence[sequenceId]) {
      const { action, frameIndex } = actionInfo;

      // Ensure the frame exists
      while (updatedSequence.frames.length <= frameIndex) {
        updatedSequence.frames.push([]);
      }

      // Add the action to the frame
      updatedSequence.frames[frameIndex].push(deepClone(action));
    }

    // Make sure we have wait actions when frame is empty
    for (let i = 0; i < updatedSequence.frames.length; i++) {
      if (
        !updatedSequence.frames[i] ||
        updatedSequence.frames[i].length === 0
      ) {
        updatedSequence.frames[i] = [WAIT_ACTION];
      }
    }

    // Update the sequence in the store
    await store.dispatch("updateSequenceAction", {
      id: updatedSequence.id,
      sequence: updatedSequence,
      undoable: true,
    });
  }
};

/**
 * Handles sequence actions when moving an element to another workspace
 * @param {string} elementId - The ID of the element being moved
 * @param {string} sourceWorkspaceId - The ID of the source workspace
 * @param {string} targetWorkspaceId - The ID of the target workspace
 * @param {boolean} recreateActions - Whether to recreate actions in the target workspace
 * @returns {Promise<void>}
 */
/**
 * Imports an existing sequence into the current sequence at the current frame
 * @param {Object} targetSequence - The sequence to add to
 * @param {Object} sourceSequence - The sequence to import
 * @returns {Object} Result object with success status and message
 */
export const importSequence = async (targetSequence, sourceSequence) => {
  if (!targetSequence || !sourceSequence) {
    return { success: false, message: "Invalid sequences provided" };
  }
  // No longer restricting import to be at or after the last frame
  const currentFrame = Math.floor(targetSequence.currentFrame);

  // If we're beyond the last frame, we need to add wait actions to fill the gap
  if (currentFrame > targetSequence.frames.length - 1) {
    for (let i = targetSequence.frames.length; i <= currentFrame; i++) {
      if (!targetSequence.frames[i]) {
        targetSequence.frames[i] = [WAIT_ACTION];
      }
    }
  }

  // Create a deep clone of the target sequence to avoid direct mutation
  const updatedSequence = deepClone(targetSequence);
  // Start from the next frame (current frame + 1) to account for the offset
  // This is where we'll add the imported sequence marker and the first action
  const startFrame = Math.floor(updatedSequence.currentFrame);

  // Generate a single importGroupId for all actions from this import
  const importGroupId = uuidv4();

  // Add a marker action for the imported sequence at the start frame
  // Ensure the start frame exists
  if (!updatedSequence.frames[startFrame + 1]) {
    updatedSequence.frames[startFrame + 1] = [];
  }

  updatedSequence.frames[startFrame + 2] =
    updatedSequence.frames[startFrame + 2] || []; // for the second zoom in

  // Remove any wait actions from the start frame
  removeWaitAction(updatedSequence.frames[startFrame]);

  // Check if the source and target sequences are in different workspaces
  const isDifferentWorkspace =
    sourceSequence.parentWorkspaceId !== targetSequence.parentWorkspaceId;

  // Find the element that contains the workspace of the imported sequence
  // Only needed if we're importing from a different workspace
  const workspaceElement = isDifferentWorkspace
    ? Array.from(store.state.allSimpleElements.values()).find(
        (element) =>
          element.childWorkspaceId === sourceSequence.parentWorkspaceId,
      )
    : null;

  // Add a zoom in transition action before the imported sequence marker
  // Only if we're importing from a different workspace
  if (isDifferentWorkspace && workspaceElement) {
    const zoomInAction1 = {
      metadata: {
        type: WORKSPACE_ZOOM_IN_1,
        duration: 0.5,
      },
      data: {
        elementId: workspaceElement.id,
      },
      importData: {
        workspaceId: targetSequence.parentWorkspaceId,
        importGroupId: importGroupId, // Same ID for all actions from this import
      },
    };
    const zoomInAction2 = {
      metadata: {
        type: WORKSPACE_ZOOM_IN_2,
        duration: 0.5,
      },
      data: {
        elementId: workspaceElement.id,
      },
      importData: {
        workspaceId: sourceSequence.parentWorkspaceId,
        importGroupId: importGroupId, // Same ID for all actions from this import
      },
    };
    updatedSequence.frames[startFrame + 1].push(zoomInAction1);
    updatedSequence.frames[startFrame + 2].push(zoomInAction2);
  }

  // Add the marker action to the start frame
  const markerAction = {
    metadata: {
      type: IMPORTED_SEQUENCE,
      duration: 1,
    },
    data: {
      totalFrames: sourceSequence.frames.length + 4,
      visualFrames: sourceSequence.frames.length - 1 + 4, // For visual representation
      isFirstFrame: true,
    },
    importData: {
      sourceSequenceId: sourceSequence.id, // Add the source sequence ID
      sourceSequenceName: sourceSequence.name,
      importGroupId: importGroupId, // Same ID for all actions from this import
      workspaceId: sourceSequence.parentWorkspaceId,
    },
  };
  updatedSequence.frames[startFrame].push(markerAction);

  // Copy actions from the source sequence to the target sequence
  const offset = isDifferentWorkspace ? 2 : 0;
  for (let i = 0; i < sourceSequence.frames.length; i++) {
    const sourceFrame = sourceSequence.frames[i] || [];
    const targetFrameIndex = startFrame + offset + i;

    // Ensure the frame exists
    if (!updatedSequence.frames[targetFrameIndex]) {
      updatedSequence.frames[targetFrameIndex] = [];
    }

    // Copy all actions from the source frame
    for (const sourceAction of sourceFrame) {
      // Skip imported sequence actions to avoid nesting
      if (sourceAction.metadata.type === IMPORTED_SEQUENCE) {
        continue;
      }

      // Create a deep copy of the source action
      const actionCopy = deepClone(sourceAction);

      // Add the imported sequence data
      actionCopy.importData = {
        sequenceId: sourceSequence.id,
        sequenceName: sourceSequence.name,
        frameIndex: i,
        importGroupId: importGroupId, // Same ID for all actions from this import
        workspaceId: sourceSequence.parentWorkspaceId,
      };

      // Add the action to the frame
      updatedSequence.frames[targetFrameIndex].push(actionCopy);
    }
    if (updatedSequence.frames[targetFrameIndex].length > 1) {
      removeWaitAction(updatedSequence.frames[targetFrameIndex]);
    }
  }

  // Add a zoom out transition action after the last frame of the imported sequence
  // Only if we're importing from a different workspace
  if (isDifferentWorkspace) {
    const lastFrameIndex = startFrame + 2 + sourceSequence.frames.length - 1;
    const nextFrameIndex = lastFrameIndex + 1;

    // Ensure the next frame exists
    if (!updatedSequence.frames[nextFrameIndex]) {
      updatedSequence.frames[nextFrameIndex] = [];
    }

    updatedSequence.frames[nextFrameIndex + 1] =
      updatedSequence.frames[nextFrameIndex + 1] || []; // For the 2nd zoom out

    // Add the zoom out action if we found a workspace element
    if (workspaceElement) {
      const zoomOutAction1 = {
        metadata: {
          type: WORKSPACE_ZOOM_OUT_1,
          duration: 0.5,
        },
        data: {
          elementId: workspaceElement.id,
        },
        importData: {
          workspaceId: sourceSequence.parentWorkspaceId,
          importGroupId: importGroupId, // Same ID for all actions from this import
        },
      };
      const zoomOutAction2 = {
        metadata: {
          type: WORKSPACE_ZOOM_OUT_2,
          duration: 0.5,
        },
        data: {
          elementId: workspaceElement.id,
        },
        importData: {
          workspaceId: targetSequence.parentWorkspaceId,
          importGroupId: importGroupId, // Same ID for all actions from this import
        },
      };
      updatedSequence.frames[nextFrameIndex].push(zoomOutAction1);
      updatedSequence.frames[nextFrameIndex + 1].push(zoomOutAction2);
      removeWaitAction(updatedSequence.frames[nextFrameIndex]);
      removeWaitAction(updatedSequence.frames[nextFrameIndex + 1]);
    }
  }

  // Update the sequence in the store
  await store.dispatch("updateSequenceAction", {
    id: updatedSequence.id,
    sequence: updatedSequence,
    undoable: true,
  });

  return {
    success: true,
    message: `Successfully imported sequence '${sourceSequence.name}' into '${targetSequence.name}'`,
  };
};

/**
 * Moves an imported sequence to a different frame
 * @param {Object} sequence - The sequence containing the imported sequence
 * @param {string} importGroupId - The ID of the imported sequence
 * @param {number} sourceFrameIndex - The current frame index of the imported sequence (display frame index)
 * @param {number} targetFrameIndex - The target frame index to move to (display frame index)
 * @returns {Object} Result object with success status and message
 */
export const moveImportedSequence = async (
  sequence,
  importGroupId,
  sourceFrameIndex,
  targetFrameIndex,
) => {
  if (!sequence) return { success: false, message: "Invalid sequence" };

  // Restrict moving to negative frames
  if (targetFrameIndex < 0) {
    return {
      success: false,
      message: "Cannot move imported sequence to a negative frame",
    };
  }

  // Clone the sequence for modification
  const updatedSequence = deepClone(sequence);

  // Convert from display frame to data frame
  // For the target, we need to add 1 to convert from display frame to data frame
  const targetDataFrame = targetFrameIndex + 1;

  // Convert source frame index to data frame
  const sourceDataFrame = sourceFrameIndex + 1;

  // Find all actions with this import group ID
  const actionsToMove = [];

  // First, collect all actions and their frame indices
  for (let i = 0; i < updatedSequence.frames.length; i++) {
    const frame = updatedSequence.frames[i] || [];

    for (let j = 0; j < frame.length; j++) {
      const action = frame[j];

      // Check for marker actions, transition actions, or imported actions with this import group ID
      if (
        (action &&
          action.metadata &&
          (action.metadata.type === IMPORTED_SEQUENCE ||
            action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
            action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
            action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
            action.metadata.type === WORKSPACE_ZOOM_OUT_2) &&
          action.importData &&
          action.importData.importGroupId === importGroupId) ||
        (action.importData && action.importData.importGroupId === importGroupId)
      ) {
        actionsToMove.push({
          frameIndex: i,
          actionIndex: j,
          isMarker:
            action.metadata.type === IMPORTED_SEQUENCE ||
            action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
            action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
            action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
            action.metadata.type === WORKSPACE_ZOOM_OUT_2,
        });
      }
    }
  }

  // If no actions found, return error
  if (actionsToMove.length === 0) {
    return {
      success: false,
      message: "No imported sequence actions found with the specified ID",
    };
  }

  // Calculate the offset between the original position and the target position
  const frameOffset = targetDataFrame - sourceDataFrame;

  // We don't need to sort the actions anymore since we're manually moving them

  // Instead of using moveActionToFrame, let's manually move the actions to ensure they're in the right place
  // First, collect all the actions we need to move
  const actionsWithNewFrames = [];
  for (const { frameIndex, actionIndex, isMarker } of actionsToMove) {
    const action =
      updatedSequence.frames[frameIndex] &&
      updatedSequence.frames[frameIndex][actionIndex];
    if (action) {
      let newFrameIndex = frameIndex + frameOffset;

      // Special handling for frame 0
      if (
        newFrameIndex === 0 &&
        action.metadata &&
        action.metadata.type === SEND_DATA
      ) {
        // sendData actions can never be at frame 0, move to frame 1 instead
        newFrameIndex = 1;
      }

      actionsWithNewFrames.push({
        action: deepClone(action),
        newFrameIndex,
        isMarker,
      });
    }
  }

  // Now remove all the actions from their original frames
  for (let i = 0; i < updatedSequence.frames.length; i++) {
    if (updatedSequence.frames[i]) {
      // Filter out actions that are part of the imported sequence
      updatedSequence.frames[i] = updatedSequence.frames[i].filter((action) => {
        return !(
          (action.metadata &&
            (action.metadata.type === IMPORTED_SEQUENCE ||
              action.metadata.type === WORKSPACE_ZOOM_IN_1 ||
              action.metadata.type === WORKSPACE_ZOOM_IN_2 ||
              action.metadata.type === WORKSPACE_ZOOM_OUT_1 ||
              action.metadata.type === WORKSPACE_ZOOM_OUT_2) &&
            action.importData &&
            action.importData.importGroupId === importGroupId) ||
          (action.importData &&
            action.importData.importGroupId === importGroupId)
        );
      });

      // If the frame is now empty and it's not frame 0, add a wait action
      if (updatedSequence.frames[i].length === 0 && i > 0) {
        updatedSequence.frames[i] = [
          {
            metadata: { type: WAIT },
            data: {
              color:
                updatedSequence.defaultFrameColor ||
                store.state.defaultFrameColor,
            },
          },
        ];
      }
    }
  }

  // Now add the actions to their new frames
  for (const { action, newFrameIndex } of actionsWithNewFrames) {
    // Ensure the target frame exists
    while (updatedSequence.frames.length <= newFrameIndex) {
      updatedSequence.frames.push([]);
    }

    if (!updatedSequence.frames[newFrameIndex]) {
      updatedSequence.frames[newFrameIndex] = [];
    }

    // Add the action to the new frame
    updatedSequence.frames[newFrameIndex].push(action);

    // Remove any wait actions from this frame
    removeWaitAction(updatedSequence.frames[newFrameIndex]);
  }

  // Fill in any gaps with wait actions
  // Find the last frame with an action
  let lastFrameIndex = 0;
  for (let i = updatedSequence.frames.length - 1; i >= 0; i--) {
    if (updatedSequence.frames[i] && updatedSequence.frames[i].length > 0) {
      lastFrameIndex = i;
      break;
    }
  }

  // Fill in gaps with wait actions
  for (let i = 1; i < lastFrameIndex; i++) {
    if (!updatedSequence.frames[i] || updatedSequence.frames[i].length === 0) {
      // Add a wait action to this frame
      updatedSequence.frames[i] = [
        {
          metadata: { type: WAIT },
          data: {
            color:
              updatedSequence.defaultFrameColor ||
              store.state.defaultFrameColor,
          },
        },
      ];
    }
  }

  // Only remove trailing frames with only wait actions when moving from end to beginning
  // Check if we're moving from a higher frame index to a lower one
  if (sourceDataFrame > targetDataFrame) {
    // First, find the last frame with a non-wait action
    let lastNonWaitFrameIndex = -1;
    for (let i = updatedSequence.frames.length - 1; i >= 0; i--) {
      const frame = updatedSequence.frames[i];
      if (
        frame &&
        frame.length > 0 &&
        !(
          frame.length === 1 &&
          frame[0].metadata &&
          frame[0].metadata.type === WAIT
        )
      ) {
        // Found a frame with non-wait actions
        lastNonWaitFrameIndex = i;
        break;
      }
    }

    // If we found a frame with non-wait actions, remove all frames after it
    if (
      lastNonWaitFrameIndex >= 0 &&
      lastNonWaitFrameIndex < updatedSequence.frames.length - 1
    ) {
      updatedSequence.frames.splice(lastNonWaitFrameIndex + 1);
    }

    // We should NOT remove wait-only frames that are between frames with non-wait actions
    // when moving an imported sequence to a specific frame. This would cause the sequence
    // to be moved to an incorrect frame.
  }

  // Update the sequence in the store
  await store.dispatch("updateSequenceAction", {
    id: updatedSequence.id,
    sequence: updatedSequence,
    undoable: true,
  });

  return {
    success: true,
    message: `Successfully moved imported sequence to frame ${targetFrameIndex}`,
  };
};

export const handleSequenceActionsForMovedElement = async (
  elementId,
  sourceWorkspaceId,
  targetWorkspaceId,
  recreateActions = true,
) => {
  // Remove actions from source workspace and get the removed actions
  const removedActions = await removeElementActionsFromSequences(
    elementId,
    sourceWorkspaceId,
  );

  // If recreateActions is true, recreate the actions in the target workspace
  if (recreateActions && removedActions.length > 0) {
    await recreateElementActionsInWorkspace(
      elementId,
      targetWorkspaceId,
      removedActions,
    );
  }
};

/**
 * Checks if an element is from an imported sequence in the current frame
 * @param {string} elementId - The ID of the element to check
 * @returns {boolean} - True if the element is from an imported sequence, false otherwise
 */
export const isElementFromImportedSequence = (elementId) => {
  // Only relevant in playback or recording mode with a sequence
  if (store.state.diagramMode === diagramMode.creationMode) {
    return false;
  }

  const sequence = store.state.sequences.get(store.state.currentSequenceId);
  if (!sequence) {
    return false;
  }

  const frameIndex = Math.floor(sequence.currentFrame) + 1;
  const frame = sequence.frames[frameIndex];
  if (!frame) {
    return false;
  }

  // Find workspace corresponding to first action found that is imported
  const workspaceId =
    frame.find((action) => action.importData?.workspaceId)?.importData
      .workspaceId ||
    store.state.sequences.get(
      frame.find((action) => action.importData?.sequenceId)?.importData
        .sequenceId,
    )?.parentWorkspaceId;
  if (!workspaceId || workspaceId === store.state.currentWorkspaceId) {
    // a little bit dodgy but ok for now
    return false;
  }

  const element = store.state.allSimpleElements.get(elementId);

  return element.parentWorkspaceIds.includes(workspaceId);
};
